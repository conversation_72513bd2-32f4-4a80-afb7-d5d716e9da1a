# Server Configuration
NODE_ENV=development
PORT=3000

# Database Configuration
DB_HOST=localhost
DB_PORT=5432
DB_NAME=orderflow_pro
DB_USER=postgres
DB_PASSWORD=your_database_password
DB_POOL_MAX=20
DB_IDLE_TIMEOUT=30000
DB_CONNECTION_TIMEOUT=2000

# Redis Configuration
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=0

# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key-change-in-production
JWT_EXPIRES_IN=24h
JWT_REFRESH_EXPIRES_IN=7d

# SendGrid Configuration (Email)
SENDGRID_API_KEY=your_sendgrid_api_key
SENDGRID_FROM_EMAIL=<EMAIL>
SENDGRID_FROM_NAME=OrderFlow Pro

# Twilio Configuration (SMS)
TWILIO_ACCOUNT_SID=your_twilio_account_sid
TWILIO_AUTH_TOKEN=your_twilio_auth_token
TWILIO_FROM_NUMBER=+**********

# Shipping Carriers Configuration
# UPS
UPS_API_KEY=your_ups_api_key
UPS_USERNAME=your_ups_username
UPS_PASSWORD=your_ups_password
UPS_SANDBOX=true

# FedEx
FEDEX_API_KEY=your_fedex_api_key
FEDEX_SECRET_KEY=your_fedex_secret_key
FEDEX_ACCOUNT_NUMBER=your_fedex_account_number
FEDEX_METER_NUMBER=your_fedex_meter_number
FEDEX_SANDBOX=true

# USPS
USPS_USER_ID=your_usps_user_id
USPS_PASSWORD=your_usps_password
USPS_SANDBOX=true

# Payment Processors Configuration
# Stripe
STRIPE_SECRET_KEY=your_stripe_secret_key
STRIPE_PUBLISHABLE_KEY=your_stripe_publishable_key
STRIPE_WEBHOOK_SECRET=your_stripe_webhook_secret

# PayPal
PAYPAL_CLIENT_ID=your_paypal_client_id
PAYPAL_CLIENT_SECRET=your_paypal_client_secret
PAYPAL_SANDBOX=true

# File Storage Configuration
STORAGE_TYPE=local
LOCAL_UPLOAD_PATH=./uploads

# AWS S3 (if using S3 storage)
AWS_ACCESS_KEY_ID=your_aws_access_key_id
AWS_SECRET_ACCESS_KEY=your_aws_secret_access_key
AWS_REGION=us-east-1
AWS_S3_BUCKET=your_s3_bucket_name

# Rate Limiting Configuration
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX=100
RATE_LIMIT_SKIP_SUCCESSFUL=false

# CORS Configuration
ALLOWED_ORIGINS=http://localhost:3000,http://localhost:3001

# Logging Configuration
LOG_LEVEL=info
LOG_FILE=./logs/app.log
LOG_MAX_SIZE=20m
LOG_MAX_FILES=5

# Business Logic Configuration
LOW_STOCK_THRESHOLD=10
AUTO_REORDER_ENABLED=true
ORDER_TIMEOUT_MINUTES=30
MAX_ORDER_ITEMS=100
FREE_SHIPPING_THRESHOLD=50.00
DEFAULT_SHIPPING_COST=9.99
RETURN_WINDOW_DAYS=30
RESTOCKING_FEE_PERCENT=0.15
EMAIL_NOTIFICATIONS_ENABLED=true
SMS_NOTIFICATIONS_ENABLED=true
ANALYTICS_RETENTION_DAYS=365
REALTIME_ANALYTICS_ENABLED=true

# Cache Configuration
CACHE_DEFAULT_TTL=3600
CACHE_INVENTORY_TTL=300
CACHE_ORDER_TTL=1800
CACHE_ANALYTICS_TTL=3600

# External API Configuration
EXTERNAL_API_TIMEOUT=30000
EXTERNAL_API_RETRIES=3
EXTERNAL_API_RETRY_DELAY=1000

# Channel-Specific Configuration (Example for multiple channels)
# Shopify Channel 1
SHOPIFY_SHOP_DOMAIN_channel_id_1=your-shop.myshopify.com
SHOPIFY_ACCESS_TOKEN_channel_id_1=your_shopify_access_token
SHOPIFY_LOCATION_ID_channel_id_1=your_shopify_location_id

# Amazon Channel 1
AMAZON_MARKETPLACE_ID_channel_id_2=your_amazon_marketplace_id
AMAZON_ACCESS_TOKEN_channel_id_2=your_amazon_access_token
AMAZON_REFRESH_TOKEN_channel_id_2=your_amazon_refresh_token
AMAZON_CLIENT_ID_channel_id_2=your_amazon_client_id
AMAZON_CLIENT_SECRET_channel_id_2=your_amazon_client_secret

# eBay Channel 1
EBAY_ACCESS_TOKEN_channel_id_3=your_ebay_access_token
EBAY_REFRESH_TOKEN_channel_id_3=your_ebay_refresh_token

# Admin Configuration
ADMIN_EMAIL=<EMAIL>
INVENTORY_MANAGER_EMAIL=<EMAIL>
SUPPORT_EMAIL=<EMAIL>

# URLs
RETURN_POLICY_URL=https://orderflowpro.com/return-policy
PRIVACY_POLICY_URL=https://orderflowpro.com/privacy-policy
TERMS_OF_SERVICE_URL=https://orderflowpro.com/terms
