-- Initial seed data for E-commerce Order Processing Automation

-- Insert sample sales channels
INSERT INTO sales_channels (id, name, type, api_endpoint, is_active) VALUES
    (uuid_generate_v4(), 'Main Web Store', 'web_store', 'https://api.webstore.com/orders', true),
    (uuid_generate_v4(), 'Mobile App', 'mobile_app', 'https://api.mobileapp.com/orders', true),
    (uuid_generate_v4(), 'Amazon Marketplace', 'marketplace', 'https://api.amazon.com/orders', true),
    (uuid_generate_v4(), 'eBay Store', 'marketplace', 'https://api.ebay.com/orders', true),
    (uuid_generate_v4(), 'Third Party API', 'api', 'https://api.thirdparty.com/orders', true);

-- Insert sample categories
INSERT INTO categories (id, name, description) VALUES
    (uuid_generate_v4(), 'Electronics', 'Electronic devices and accessories'),
    (uuid_generate_v4(), 'Clothing', 'Apparel and fashion items'),
    (uuid_generate_v4(), 'Home & Garden', 'Home improvement and garden supplies'),
    (uuid_generate_v4(), 'Sports & Outdoors', 'Sports equipment and outdoor gear'),
    (uuid_generate_v4(), 'Books', 'Books and educational materials');

-- Insert sample fulfillment centers
INSERT INTO fulfillment_centers (id, name, code, address, coordinates, capacity, is_active) VALUES
    (
        uuid_generate_v4(), 
        'East Coast Fulfillment Center', 
        'EC-FC-01',
        '{"street": "123 Warehouse Blvd", "city": "Newark", "state": "NJ", "postal_code": "07102", "country": "USA"}',
        POINT(-74.1723667, 40.7356567),
        10000,
        true
    ),
    (
        uuid_generate_v4(), 
        'West Coast Fulfillment Center', 
        'WC-FC-01',
        '{"street": "456 Distribution Ave", "city": "Los Angeles", "state": "CA", "postal_code": "90021", "country": "USA"}',
        POINT(-118.2436849, 34.0522265),
        15000,
        true
    ),
    (
        uuid_generate_v4(), 
        'Central Fulfillment Center', 
        'CT-FC-01',
        '{"street": "789 Logistics Dr", "city": "Chicago", "state": "IL", "postal_code": "60607", "country": "USA"}',
        POINT(-87.6297982, 41.8781136),
        12000,
        true
    );

-- Insert sample vendors
INSERT INTO vendors (id, name, contact_email, contact_phone, address, payment_terms, lead_time_days, is_active) VALUES
    (
        uuid_generate_v4(),
        'TechSupply Corp',
        '<EMAIL>',
        '******-0101',
        '{"street": "100 Tech Park", "city": "Austin", "state": "TX", "postal_code": "78701", "country": "USA"}',
        'Net 30',
        5,
        true
    ),
    (
        uuid_generate_v4(),
        'Fashion Wholesale Inc',
        '<EMAIL>',
        '******-0102',
        '{"street": "200 Fashion Ave", "city": "New York", "state": "NY", "postal_code": "10001", "country": "USA"}',
        'Net 15',
        7,
        true
    ),
    (
        uuid_generate_v4(),
        'Home Goods Distributor',
        '<EMAIL>',
        '******-0103',
        '{"street": "300 Home St", "city": "Atlanta", "state": "GA", "postal_code": "30309", "country": "USA"}',
        'Net 45',
        10,
        true
    );

-- Insert sample products
WITH category_electronics AS (SELECT id FROM categories WHERE name = 'Electronics' LIMIT 1),
     category_clothing AS (SELECT id FROM categories WHERE name = 'Clothing' LIMIT 1),
     category_home AS (SELECT id FROM categories WHERE name = 'Home & Garden' LIMIT 1)

INSERT INTO products (id, sku, name, description, category_id, price, cost, weight, dimensions, is_active) VALUES
    (
        uuid_generate_v4(),
        'ELEC-001',
        'Wireless Bluetooth Headphones',
        'High-quality wireless headphones with noise cancellation',
        (SELECT id FROM category_electronics),
        199.99,
        89.99,
        0.5,
        '{"length": 8, "width": 7, "height": 3}',
        true
    ),
    (
        uuid_generate_v4(),
        'ELEC-002',
        'Smartphone Case',
        'Protective case for smartphones',
        (SELECT id FROM category_electronics),
        29.99,
        12.99,
        0.1,
        '{"length": 6, "width": 3, "height": 0.5}',
        true
    ),
    (
        uuid_generate_v4(),
        'CLOTH-001',
        'Cotton T-Shirt',
        '100% cotton comfortable t-shirt',
        (SELECT id FROM category_clothing),
        24.99,
        8.99,
        0.2,
        '{"length": 12, "width": 8, "height": 1}',
        true
    ),
    (
        uuid_generate_v4(),
        'HOME-001',
        'Kitchen Knife Set',
        'Professional 5-piece kitchen knife set',
        (SELECT id FROM category_home),
        149.99,
        59.99,
        2.0,
        '{"length": 14, "width": 8, "height": 2}',
        true
    ),
    (
        uuid_generate_v4(),
        'ELEC-003',
        'USB-C Cable',
        'High-speed USB-C charging cable',
        (SELECT id FROM category_electronics),
        19.99,
        5.99,
        0.1,
        '{"length": 6, "width": 1, "height": 0.5}',
        true
    );

-- Insert sample customers
INSERT INTO customers (id, email, first_name, last_name, phone, created_at) VALUES
    (uuid_generate_v4(), '<EMAIL>', 'John', 'Doe', '******-1001', CURRENT_TIMESTAMP),
    (uuid_generate_v4(), '<EMAIL>', 'Jane', 'Smith', '******-1002', CURRENT_TIMESTAMP),
    (uuid_generate_v4(), '<EMAIL>', 'Mike', 'Johnson', '******-1003', CURRENT_TIMESTAMP),
    (uuid_generate_v4(), '<EMAIL>', 'Sarah', 'Wilson', '******-1004', CURRENT_TIMESTAMP),
    (uuid_generate_v4(), '<EMAIL>', 'David', 'Brown', '******-1005', CURRENT_TIMESTAMP);

-- Insert sample inventory for each fulfillment center
WITH 
    products_list AS (SELECT id, sku FROM products),
    fc_list AS (SELECT id FROM fulfillment_centers)

INSERT INTO inventory (product_id, fulfillment_center_id, quantity_available, quantity_reserved, reorder_point, max_stock_level)
SELECT 
    p.id,
    fc.id,
    CASE 
        WHEN p.sku LIKE 'ELEC%' THEN 100 + (RANDOM() * 200)::INTEGER
        WHEN p.sku LIKE 'CLOTH%' THEN 200 + (RANDOM() * 300)::INTEGER
        ELSE 50 + (RANDOM() * 100)::INTEGER
    END as quantity_available,
    0 as quantity_reserved,
    CASE 
        WHEN p.sku LIKE 'ELEC%' THEN 20
        WHEN p.sku LIKE 'CLOTH%' THEN 50
        ELSE 10
    END as reorder_point,
    CASE 
        WHEN p.sku LIKE 'ELEC%' THEN 500
        WHEN p.sku LIKE 'CLOTH%' THEN 1000
        ELSE 200
    END as max_stock_level
FROM products_list p
CROSS JOIN fc_list fc;

-- Insert vendor-product relationships
WITH 
    vendor_tech AS (SELECT id FROM vendors WHERE name = 'TechSupply Corp' LIMIT 1),
    vendor_fashion AS (SELECT id FROM vendors WHERE name = 'Fashion Wholesale Inc' LIMIT 1),
    vendor_home AS (SELECT id FROM vendors WHERE name = 'Home Goods Distributor' LIMIT 1),
    product_headphones AS (SELECT id FROM products WHERE sku = 'ELEC-001' LIMIT 1),
    product_case AS (SELECT id FROM products WHERE sku = 'ELEC-002' LIMIT 1),
    product_tshirt AS (SELECT id FROM products WHERE sku = 'CLOTH-001' LIMIT 1),
    product_knives AS (SELECT id FROM products WHERE sku = 'HOME-001' LIMIT 1),
    product_cable AS (SELECT id FROM products WHERE sku = 'ELEC-003' LIMIT 1)

INSERT INTO vendor_products (vendor_id, product_id, vendor_sku, cost, minimum_order_quantity, is_preferred) VALUES
    ((SELECT id FROM vendor_tech), (SELECT id FROM product_headphones), 'TECH-HP-001', 89.99, 10, true),
    ((SELECT id FROM vendor_tech), (SELECT id FROM product_case), 'TECH-CASE-001', 12.99, 50, true),
    ((SELECT id FROM vendor_tech), (SELECT id FROM product_cable), 'TECH-CABLE-001', 5.99, 100, true),
    ((SELECT id FROM vendor_fashion), (SELECT id FROM product_tshirt), 'FASH-SHIRT-001', 8.99, 25, true),
    ((SELECT id FROM vendor_home), (SELECT id FROM product_knives), 'HOME-KNIFE-001', 59.99, 5, true);

-- Insert sample daily sales metrics for the last 30 days
WITH 
    date_series AS (
        SELECT generate_series(
            CURRENT_DATE - INTERVAL '30 days',
            CURRENT_DATE - INTERVAL '1 day',
            INTERVAL '1 day'
        )::DATE as date
    ),
    channels AS (SELECT id FROM sales_channels LIMIT 3)

INSERT INTO daily_sales_metrics (date, channel_id, total_orders, total_revenue, total_items_sold, average_order_value)
SELECT 
    ds.date,
    c.id,
    (5 + RANDOM() * 20)::INTEGER as total_orders,
    (500 + RANDOM() * 2000)::DECIMAL(12,2) as total_revenue,
    (10 + RANDOM() * 50)::INTEGER as total_items_sold,
    (50 + RANDOM() * 150)::DECIMAL(10,2) as average_order_value
FROM date_series ds
CROSS JOIN channels c;

-- Create some sample customer addresses
WITH customers_sample AS (
    SELECT id, first_name, last_name FROM customers LIMIT 3
)
INSERT INTO customer_addresses (customer_id, type, street_address, city, state, postal_code, country, is_default)
SELECT 
    id,
    'shipping',
    CASE 
        WHEN first_name = 'John' THEN '123 Main St'
        WHEN first_name = 'Jane' THEN '456 Oak Ave'
        ELSE '789 Pine Rd'
    END,
    CASE 
        WHEN first_name = 'John' THEN 'New York'
        WHEN first_name = 'Jane' THEN 'Los Angeles'
        ELSE 'Chicago'
    END,
    CASE 
        WHEN first_name = 'John' THEN 'NY'
        WHEN first_name = 'Jane' THEN 'CA'
        ELSE 'IL'
    END,
    CASE 
        WHEN first_name = 'John' THEN '10001'
        WHEN first_name = 'Jane' THEN '90210'
        ELSE '60601'
    END,
    'USA',
    true
FROM customers_sample;
