# OrderFlow Pro API Documentation

## Overview

The OrderFlow Pro API provides comprehensive endpoints for e-commerce order processing automation, inventory management, customer communication, and analytics.

**Base URL:** `http://localhost:3000/api`

**Authentication:** JWT Bearer Token

## Authentication

### Login
```http
POST /auth/login
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "password"
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "user": {
      "id": "uuid",
      "email": "<EMAIL>",
      "role": "admin"
    }
  }
}
```

## Orders API

### Create Order
```http
POST /api/orders
Authorization: Bearer {token}
Content-Type: application/json

{
  "customer_id": "uuid",
  "channel_id": "uuid",
  "items": [
    {
      "product_id": "uuid",
      "quantity": 2,
      "unit_price": 29.99
    }
  ],
  "billing_address": {
    "street_address": "123 Main St",
    "city": "New York",
    "state": "NY",
    "postal_code": "10001",
    "country": "USA"
  },
  "shipping_address": {
    "street_address": "123 Main St",
    "city": "New York",
    "state": "NY",
    "postal_code": "10001",
    "country": "USA"
  },
  "shipping_method": "standard",
  "notes": "Handle with care"
}
```

### Get Orders
```http
GET /api/orders?page=1&limit=20&status=pending&search=ORD-123
Authorization: Bearer {token}
```

**Query Parameters:**
- `page` (integer): Page number (default: 1)
- `limit` (integer): Items per page (default: 20, max: 100)
- `status` (string): Order status filter
- `payment_status` (string): Payment status filter
- `channel_id` (uuid): Filter by sales channel
- `customer_id` (uuid): Filter by customer
- `date_from` (ISO date): Start date filter
- `date_to` (ISO date): End date filter
- `search` (string): Search in order number, customer email, or name

### Get Order by ID
```http
GET /api/orders/{id}
Authorization: Bearer {token}
```

### Update Order
```http
PUT /api/orders/{id}
Authorization: Bearer {token}
Content-Type: application/json

{
  "status": "processing",
  "tracking_number": "1Z999AA1234567890",
  "shipping_method": "UPS Ground",
  "notes": "Updated shipping information"
}
```

### Process Order
```http
POST /api/orders/{id}/process
Authorization: Bearer {token}
```

### Ship Order
```http
POST /api/orders/{id}/ship
Authorization: Bearer {token}
Content-Type: application/json

{
  "tracking_number": "1Z999AA1234567890",
  "shipping_method": "UPS Ground"
}
```

### Cancel Order
```http
DELETE /api/orders/{id}
Authorization: Bearer {token}
```

## Inventory API

### Get Inventory
```http
GET /api/inventory?page=1&limit=20&low_stock=true
Authorization: Bearer {token}
```

### Update Inventory
```http
PUT /api/inventory/{product_id}/{fulfillment_center_id}
Authorization: Bearer {token}
Content-Type: application/json

{
  "quantity_change": 100,
  "reason": "restock",
  "reference_id": "PO-123456"
}
```

### Check Availability
```http
GET /api/inventory/availability/{product_id}?quantity=5&fulfillment_center_id={fc_id}
Authorization: Bearer {token}
```

### Bulk Update Inventory
```http
POST /api/inventory/bulk-update
Authorization: Bearer {token}
Content-Type: application/json

{
  "updates": [
    {
      "product_id": "uuid",
      "fulfillment_center_id": "uuid",
      "quantity_change": 50,
      "reason": "restock"
    }
  ]
}
```

## Customers API

### Create Customer
```http
POST /api/customers
Authorization: Bearer {token}
Content-Type: application/json

{
  "email": "<EMAIL>",
  "first_name": "John",
  "last_name": "Doe",
  "phone": "+1234567890",
  "date_of_birth": "1990-01-01"
}
```

### Get Customers
```http
GET /api/customers?page=1&limit=20&search=john
Authorization: Bearer {token}
```

### Get Customer Orders
```http
GET /api/customers/{id}/orders
Authorization: Bearer {token}
```

## Returns API

### Create Return Request
```http
POST /api/returns
Authorization: Bearer {token}
Content-Type: application/json

{
  "order_id": "uuid",
  "customer_id": "uuid",
  "reason": "defective",
  "items": [
    {
      "order_item_id": "uuid",
      "quantity": 1,
      "condition": "damaged"
    }
  ]
}
```

### Approve Return
```http
POST /api/returns/{id}/approve
Authorization: Bearer {token}
```

### Process Return
```http
POST /api/returns/{id}/process
Authorization: Bearer {token}
Content-Type: application/json

{
  "received_items": [
    {
      "order_item_id": "uuid",
      "quantity_received": 1,
      "condition": "used"
    }
  ]
}
```

## Vendors API

### Create Purchase Order
```http
POST /api/vendors/purchase-orders
Authorization: Bearer {token}
Content-Type: application/json

{
  "vendor_id": "uuid",
  "items": [
    {
      "product_id": "uuid",
      "quantity": 100,
      "unit_cost": 15.99
    }
  ],
  "expected_delivery_date": "2024-02-01",
  "notes": "Urgent restock"
}
```

### Auto-Generate Purchase Orders
```http
POST /api/vendors/auto-generate-pos
Authorization: Bearer {token}
```

### Receive Purchase Order
```http
POST /api/vendors/purchase-orders/{id}/receive
Authorization: Bearer {token}
Content-Type: application/json

{
  "received_items": [
    {
      "po_item_id": "uuid",
      "quantity_received": 95,
      "fulfillment_center_id": "uuid"
    }
  ]
}
```

## Analytics API

### Get Dashboard Data
```http
GET /api/analytics/dashboard?date_from=2024-01-01&date_to=2024-01-31
Authorization: Bearer {token}
```

### Get Sales Metrics
```http
GET /api/analytics/sales?date_from=2024-01-01&date_to=2024-01-31&granularity=daily
Authorization: Bearer {token}
```

### Get Inventory Analytics
```http
GET /api/analytics/inventory
Authorization: Bearer {token}
```

### Get Customer Analytics
```http
GET /api/analytics/customers?date_from=2024-01-01&date_to=2024-01-31
Authorization: Bearer {token}
```

### Export Analytics
```http
GET /api/analytics/export/sales?date_from=2024-01-01&date_to=2024-01-31&format=csv
Authorization: Bearer {token}
```

## Webhooks

### Order Webhook (External Channels)
```http
POST /api/orders/webhook/{channel_id}
Content-Type: application/json
X-API-Key: {channel_api_key}

{
  "external_order_id": "ext_123456",
  "customer_email": "<EMAIL>",
  "items": [
    {
      "sku": "PROD-001",
      "quantity": 2,
      "price": 29.99
    }
  ],
  "shipping_address": {
    "name": "John Doe",
    "street": "123 Main St",
    "city": "New York",
    "state": "NY",
    "zip": "10001",
    "country": "US"
  }
}
```

## Error Responses

All API endpoints return consistent error responses:

```json
{
  "status": "error",
  "message": "Validation failed",
  "type": "validation",
  "field": "email",
  "timestamp": "2024-01-15T10:30:00Z"
}
```

### HTTP Status Codes

- `200` - Success
- `201` - Created
- `400` - Bad Request (validation error)
- `401` - Unauthorized (invalid token)
- `403` - Forbidden (insufficient permissions)
- `404` - Not Found
- `409` - Conflict (duplicate resource)
- `429` - Too Many Requests (rate limited)
- `500` - Internal Server Error

## Rate Limiting

- **Limit:** 100 requests per 15 minutes per IP
- **Headers:** 
  - `X-RateLimit-Limit`: Request limit
  - `X-RateLimit-Remaining`: Remaining requests
  - `X-RateLimit-Reset`: Reset time

## Pagination

List endpoints support pagination:

```json
{
  "success": true,
  "data": [...],
  "pagination": {
    "page": 1,
    "limit": 20,
    "total": 150,
    "totalPages": 8,
    "hasNext": true,
    "hasPrev": false
  }
}
```

## Health Check

```http
GET /health
```

**Response:**
```json
{
  "status": "OK",
  "timestamp": "2024-01-15T10:30:00Z",
  "uptime": 3600,
  "environment": "development"
}
```

## Testing

Use the provided Postman collection or test with curl:

```bash
# Get orders
curl -H "Authorization: Bearer YOUR_TOKEN" \
     http://localhost:3000/api/orders

# Create order
curl -X POST \
     -H "Authorization: Bearer YOUR_TOKEN" \
     -H "Content-Type: application/json" \
     -d '{"customer_id":"uuid","channel_id":"uuid","items":[...]}' \
     http://localhost:3000/api/orders
```

For more detailed examples and integration guides, see the `/docs` directory.
