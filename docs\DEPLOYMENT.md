# OrderFlow Pro Deployment Guide

## Overview

This guide covers deployment options for OrderFlow Pro, from local development to production environments.

## Prerequisites

- Node.js 18+ LTS
- PostgreSQL 13+
- Redis 6+
- Docker and Docker Compose (for containerized deployment)

## Local Development Setup

### 1. Clone Repository
```bash
git clone https://github.com/HectorTa1989/orderflow-pro.git
cd orderflow-pro
```

### 2. Install Dependencies
```bash
npm install
```

### 3. Environment Configuration
```bash
cp .env.example .env
# Edit .env with your configuration
```

### 4. Database Setup
```bash
# Start PostgreSQL and Redis
# Create database
createdb orderflow_pro

# Run migrations
npm run migrate

# Seed initial data
npm run seed
```

### 5. Start Development Server
```bash
npm run dev
```

The API will be available at `http://localhost:3000`

## Docker Development Setup

### 1. Quick Start with Docker Compose
```bash
# Clone repository
git clone https://github.com/HectorTa1989/orderflow-pro.git
cd orderflow-pro

# Start all services
docker-compose up -d

# View logs
docker-compose logs -f api
```

### 2. Services Available
- **API**: http://localhost:3000
- **PostgreSQL**: localhost:5432
- **Redis**: localhost:6379
- **pgAdmin**: http://localhost:5050
- **Redis Commander**: http://localhost:8081

### 3. Stop Services
```bash
docker-compose down
```

## Production Deployment

### Option 1: Traditional Server Deployment

#### 1. Server Requirements
- **CPU**: 2+ cores
- **RAM**: 4GB minimum, 8GB recommended
- **Storage**: 50GB+ SSD
- **OS**: Ubuntu 20.04+ LTS or CentOS 8+

#### 2. Install Dependencies
```bash
# Update system
sudo apt update && sudo apt upgrade -y

# Install Node.js 18
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs

# Install PostgreSQL
sudo apt install postgresql postgresql-contrib

# Install Redis
sudo apt install redis-server

# Install PM2 for process management
sudo npm install -g pm2
```

#### 3. Database Setup
```bash
# Create database user
sudo -u postgres createuser --interactive orderflow_user
sudo -u postgres createdb orderflow_pro -O orderflow_user

# Set password
sudo -u postgres psql -c "ALTER USER orderflow_user PASSWORD 'secure_password';"
```

#### 4. Application Deployment
```bash
# Clone and setup application
git clone https://github.com/HectorTa1989/orderflow-pro.git
cd orderflow-pro

# Install production dependencies
npm ci --only=production

# Configure environment
cp .env.example .env
# Edit .env with production values

# Run database migrations
npm run migrate

# Start with PM2
pm2 start ecosystem.config.js --env production
pm2 save
pm2 startup
```

#### 5. Nginx Configuration
```nginx
server {
    listen 80;
    server_name your-domain.com;

    location / {
        proxy_pass http://localhost:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }
}
```

### Option 2: Docker Production Deployment

#### 1. Production Docker Compose
```yaml
# docker-compose.prod.yml
version: '3.8'

services:
  postgres:
    image: postgres:15-alpine
    environment:
      POSTGRES_DB: orderflow_pro
      POSTGRES_USER: orderflow_user
      POSTGRES_PASSWORD: ${DB_PASSWORD}
    volumes:
      - postgres_data:/var/lib/postgresql/data
    restart: unless-stopped

  redis:
    image: redis:7-alpine
    volumes:
      - redis_data:/data
    restart: unless-stopped

  api:
    build: .
    environment:
      NODE_ENV: production
      DB_HOST: postgres
      REDIS_HOST: redis
    depends_on:
      - postgres
      - redis
    restart: unless-stopped

  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/prod.conf:/etc/nginx/nginx.conf
      - ./ssl:/etc/nginx/ssl
    depends_on:
      - api
    restart: unless-stopped

volumes:
  postgres_data:
  redis_data:
```

#### 2. Deploy with Docker
```bash
# Build and start production services
docker-compose -f docker-compose.prod.yml up -d

# Monitor logs
docker-compose -f docker-compose.prod.yml logs -f
```

### Option 3: Cloud Deployment (AWS)

#### 1. AWS ECS with Fargate
```bash
# Install AWS CLI and ECS CLI
pip install awscli
curl -Lo ecs-cli https://amazon-ecs-cli.s3.amazonaws.com/ecs-cli-linux-amd64-latest
chmod +x ecs-cli && sudo mv ecs-cli /usr/local/bin

# Configure ECS CLI
ecs-cli configure --cluster orderflow-prod --region us-east-1

# Create cluster
ecs-cli up --keypair your-key --capability-iam --size 2 --instance-type t3.medium

# Deploy application
ecs-cli compose --file docker-compose.aws.yml up
```

#### 2. AWS RDS and ElastiCache
```bash
# Create RDS PostgreSQL instance
aws rds create-db-instance \
    --db-instance-identifier orderflow-db \
    --db-instance-class db.t3.micro \
    --engine postgres \
    --master-username orderflow_user \
    --master-user-password your-password \
    --allocated-storage 20

# Create ElastiCache Redis cluster
aws elasticache create-cache-cluster \
    --cache-cluster-id orderflow-redis \
    --cache-node-type cache.t3.micro \
    --engine redis \
    --num-cache-nodes 1
```

### Option 4: Kubernetes Deployment

#### 1. Kubernetes Manifests
```yaml
# k8s/deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: orderflow-api
spec:
  replicas: 3
  selector:
    matchLabels:
      app: orderflow-api
  template:
    metadata:
      labels:
        app: orderflow-api
    spec:
      containers:
      - name: api
        image: orderflow-pro:latest
        ports:
        - containerPort: 3000
        env:
        - name: NODE_ENV
          value: "production"
        - name: DB_HOST
          value: "postgres-service"
        - name: REDIS_HOST
          value: "redis-service"
```

#### 2. Deploy to Kubernetes
```bash
# Apply manifests
kubectl apply -f k8s/

# Check deployment
kubectl get pods
kubectl get services
```

## Environment Variables

### Required Production Variables
```bash
# Application
NODE_ENV=production
PORT=3000

# Database
DB_HOST=your-db-host
DB_PASSWORD=secure-password
JWT_SECRET=your-jwt-secret

# External Services
SENDGRID_API_KEY=your-sendgrid-key
TWILIO_ACCOUNT_SID=your-twilio-sid
TWILIO_AUTH_TOKEN=your-twilio-token
```

## Security Considerations

### 1. Environment Security
- Use strong passwords for database and Redis
- Generate secure JWT secrets
- Enable SSL/TLS for all connections
- Configure firewall rules

### 2. Application Security
- Enable rate limiting
- Use HTTPS in production
- Implement proper CORS policies
- Regular security updates

### 3. Database Security
- Use connection pooling
- Enable SSL connections
- Regular backups
- Access control and monitoring

## Monitoring and Logging

### 1. Application Monitoring
```bash
# PM2 monitoring
pm2 monit

# Docker monitoring
docker stats

# Kubernetes monitoring
kubectl top pods
```

### 2. Log Management
```bash
# PM2 logs
pm2 logs

# Docker logs
docker-compose logs -f

# Centralized logging with ELK stack
# Configure Elasticsearch, Logstash, Kibana
```

### 3. Health Checks
- API health endpoint: `/health`
- Database connectivity checks
- Redis connectivity checks
- External service availability

## Backup and Recovery

### 1. Database Backup
```bash
# PostgreSQL backup
pg_dump -h localhost -U orderflow_user orderflow_pro > backup.sql

# Automated backup script
#!/bin/bash
DATE=$(date +%Y%m%d_%H%M%S)
pg_dump -h localhost -U orderflow_user orderflow_pro > "backup_$DATE.sql"
```

### 2. Redis Backup
```bash
# Redis backup
redis-cli BGSAVE
cp /var/lib/redis/dump.rdb backup/
```

### 3. Application Backup
```bash
# Code and configuration backup
tar -czf app_backup.tar.gz /path/to/orderflow-pro
```

## Scaling Considerations

### 1. Horizontal Scaling
- Load balancer configuration
- Multiple API instances
- Database read replicas
- Redis clustering

### 2. Performance Optimization
- Database indexing
- Query optimization
- Caching strategies
- CDN for static assets

### 3. Auto-scaling
- Container orchestration
- CPU/memory-based scaling
- Queue-based scaling
- Geographic distribution

## Troubleshooting

### Common Issues
1. **Database connection errors**: Check credentials and network
2. **Redis connection issues**: Verify Redis service status
3. **High memory usage**: Monitor and optimize queries
4. **Slow API responses**: Check database performance and caching

### Debug Commands
```bash
# Check service status
systemctl status postgresql redis-server

# Monitor resources
htop
iotop
netstat -tulpn

# Application logs
tail -f logs/app.log
pm2 logs --lines 100
```

## Support

For deployment issues or questions:
- GitHub Issues: https://github.com/HectorTa1989/orderflow-pro/issues
- Documentation: `/docs` directory
- Email: <EMAIL>
