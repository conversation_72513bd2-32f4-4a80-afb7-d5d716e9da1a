{"name": "orderflow-pro", "version": "1.0.0", "description": "Comprehensive E-commerce Order Processing Automation System", "main": "src/app.js", "scripts": {"start": "node src/app.js", "dev": "nodemon src/app.js", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "migrate": "node database/migrations/run-migrations.js", "seed": "psql $DATABASE_URL -f database/seeds/initial_data.sql", "lint": "eslint src/", "lint:fix": "eslint src/ --fix", "docker:build": "docker build -t orderflow-pro .", "docker:run": "docker-compose up", "docker:down": "docker-compose down"}, "keywords": ["ecommerce", "order-processing", "automation", "inventory-management", "fulfillment", "nodejs", "express", "postgresql"], "author": "HectorTa1989", "license": "MIT", "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.1.0", "morgan": "^1.10.0", "compression": "^1.7.4", "dotenv": "^16.3.1", "pg": "^8.11.3", "pg-pool": "^3.6.1", "redis": "^4.6.10", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "joi": "^17.11.0", "uuid": "^9.0.1", "moment": "^2.29.4", "lodash": "^4.17.21", "axios": "^1.6.0", "@sendgrid/mail": "^7.7.0", "twilio": "^4.19.0", "node-cron": "^3.0.3", "winston": "^3.11.0", "express-rate-limit": "^7.1.5", "express-validator": "^7.0.1", "multer": "^1.4.5-lts.1", "csv-parser": "^3.0.0", "pdf-lib": "^1.17.1", "sharp": "^0.32.6"}, "devDependencies": {"nodemon": "^3.0.1", "jest": "^29.7.0", "supertest": "^6.3.3", "eslint": "^8.54.0", "eslint-config-standard": "^17.1.0", "eslint-plugin-import": "^2.29.0", "eslint-plugin-node": "^11.1.0", "eslint-plugin-promise": "^6.1.1", "@types/jest": "^29.5.8"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "repository": {"type": "git", "url": "https://github.com/HectorTa1989/orderflow-pro.git"}, "bugs": {"url": "https://github.com/HectorTa1989/orderflow-pro/issues"}, "homepage": "https://github.com/HectorTa1989/orderflow-pro#readme", "jest": {"testEnvironment": "node", "collectCoverageFrom": ["src/**/*.js", "!src/app.js", "!src/config/**"], "coverageDirectory": "coverage", "coverageReporters": ["text", "lcov", "html"]}, "eslintConfig": {"extends": ["standard"], "env": {"node": true, "jest": true}, "rules": {"no-console": "warn", "prefer-const": "error"}}}