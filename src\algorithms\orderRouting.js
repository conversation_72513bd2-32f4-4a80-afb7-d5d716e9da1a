const logger = require('../utils/logger');

/**
 * Advanced Order Routing Algorithm
 * Implements multiple routing strategies for optimal order fulfillment
 */
class OrderRoutingAlgorithm {
  constructor() {
    this.strategies = {
      DISTANCE_BASED: 'distance_based',
      COST_OPTIMIZED: 'cost_optimized',
      INVENTORY_BALANCED: 'inventory_balanced',
      HYBRID: 'hybrid'
    };
  }

  /**
   * Route orders using specified strategy
   */
  async routeOrders(orders, fulfillmentCenters, strategy = this.strategies.HYBRID) {
    logger.info('Starting order routing', {
      order_count: orders.length,
      fulfillment_center_count: fulfillmentCenters.length,
      strategy
    });

    switch (strategy) {
      case this.strategies.DISTANCE_BASED:
        return await this.distanceBasedRouting(orders, fulfillmentCenters);
      
      case this.strategies.COST_OPTIMIZED:
        return await this.costOptimizedRouting(orders, fulfillmentCenters);
      
      case this.strategies.INVENTORY_BALANCED:
        return await this.inventoryBalancedRouting(orders, fulfillmentCenters);
      
      case this.strategies.HYBRID:
        return await this.hybridRouting(orders, fulfillmentCenters);
      
      default:
        throw new Error(`Unknown routing strategy: ${strategy}`);
    }
  }

  /**
   * Distance-based routing - prioritizes closest fulfillment center
   */
  async distanceBasedRouting(orders, fulfillmentCenters) {
    const routingResults = [];

    for (const order of orders) {
      const distances = fulfillmentCenters.map(fc => ({
        fulfillmentCenter: fc,
        distance: this.calculateDistance(
          this.parseCoordinates(fc.coordinates),
          this.getAddressCoordinates(order.shipping_address)
        )
      }));

      // Sort by distance (closest first)
      distances.sort((a, b) => a.distance - b.distance);

      // Find first FC that can fulfill the order
      let selectedFC = null;
      for (const { fulfillmentCenter, distance } of distances) {
        if (await this.canFulfillOrder(fulfillmentCenter, order)) {
          selectedFC = { fulfillmentCenter, distance };
          break;
        }
      }

      routingResults.push({
        order_id: order.id,
        selected_fulfillment_center: selectedFC?.fulfillmentCenter,
        distance: selectedFC?.distance,
        strategy: this.strategies.DISTANCE_BASED,
        success: !!selectedFC
      });
    }

    return routingResults;
  }

  /**
   * Cost-optimized routing - minimizes total fulfillment cost
   */
  async costOptimizedRouting(orders, fulfillmentCenters) {
    const routingResults = [];

    for (const order of orders) {
      const costs = await Promise.all(
        fulfillmentCenters.map(async fc => ({
          fulfillmentCenter: fc,
          cost: await this.calculateFulfillmentCost(fc, order),
          canFulfill: await this.canFulfillOrder(fc, order)
        }))
      );

      // Filter only FCs that can fulfill and sort by cost
      const viableFCs = costs
        .filter(c => c.canFulfill)
        .sort((a, b) => a.cost - b.cost);

      const selectedFC = viableFCs[0];

      routingResults.push({
        order_id: order.id,
        selected_fulfillment_center: selectedFC?.fulfillmentCenter,
        estimated_cost: selectedFC?.cost,
        strategy: this.strategies.COST_OPTIMIZED,
        success: !!selectedFC
      });
    }

    return routingResults;
  }

  /**
   * Inventory-balanced routing - distributes load across fulfillment centers
   */
  async inventoryBalancedRouting(orders, fulfillmentCenters) {
    const routingResults = [];
    const fcUtilization = new Map();

    // Initialize utilization tracking
    fulfillmentCenters.forEach(fc => {
      fcUtilization.set(fc.id, {
        orders_assigned: 0,
        total_items: 0,
        capacity_used: 0
      });
    });

    for (const order of orders) {
      const scores = await Promise.all(
        fulfillmentCenters.map(async fc => {
          const canFulfill = await this.canFulfillOrder(fc, order);
          if (!canFulfill) return null;

          const utilization = fcUtilization.get(fc.id);
          const inventoryScore = await this.calculateInventoryScore(fc, order);
          
          // Balance score: lower utilization = higher score
          const balanceScore = 100 - (utilization.capacity_used * 100 / fc.capacity);
          
          return {
            fulfillmentCenter: fc,
            score: (inventoryScore * 0.6) + (balanceScore * 0.4),
            canFulfill: true
          };
        })
      );

      // Filter and sort by score
      const viableScores = scores
        .filter(s => s !== null)
        .sort((a, b) => b.score - a.score);

      const selectedFC = viableScores[0];

      if (selectedFC) {
        // Update utilization
        const utilization = fcUtilization.get(selectedFC.fulfillmentCenter.id);
        utilization.orders_assigned++;
        utilization.total_items += order.items.reduce((sum, item) => sum + item.quantity, 0);
        utilization.capacity_used += this.estimateOrderCapacityUsage(order);
      }

      routingResults.push({
        order_id: order.id,
        selected_fulfillment_center: selectedFC?.fulfillmentCenter,
        balance_score: selectedFC?.score,
        strategy: this.strategies.INVENTORY_BALANCED,
        success: !!selectedFC
      });
    }

    return routingResults;
  }

  /**
   * Hybrid routing - combines multiple factors for optimal routing
   */
  async hybridRouting(orders, fulfillmentCenters) {
    const routingResults = [];
    const weights = {
      distance: 0.3,
      cost: 0.25,
      inventory: 0.25,
      capacity: 0.2
    };

    for (const order of orders) {
      const scores = await Promise.all(
        fulfillmentCenters.map(async fc => {
          const canFulfill = await this.canFulfillOrder(fc, order);
          if (!canFulfill) return null;

          // Calculate individual scores
          const distance = this.calculateDistance(
            this.parseCoordinates(fc.coordinates),
            this.getAddressCoordinates(order.shipping_address)
          );
          const distanceScore = Math.max(0, 100 - (distance / 30)); // Normalize to 0-100

          const cost = await this.calculateFulfillmentCost(fc, order);
          const costScore = Math.max(0, 100 - (cost / 2)); // Normalize to 0-100

          const inventoryScore = await this.calculateInventoryScore(fc, order);
          const capacityScore = await this.calculateCapacityScore(fc);

          // Calculate weighted total score
          const totalScore = 
            (distanceScore * weights.distance) +
            (costScore * weights.cost) +
            (inventoryScore * weights.inventory) +
            (capacityScore * weights.capacity);

          return {
            fulfillmentCenter: fc,
            totalScore,
            breakdown: {
              distance: distanceScore,
              cost: costScore,
              inventory: inventoryScore,
              capacity: capacityScore
            },
            canFulfill: true
          };
        })
      );

      // Filter and sort by total score
      const viableScores = scores
        .filter(s => s !== null)
        .sort((a, b) => b.totalScore - a.totalScore);

      const selectedFC = viableScores[0];

      routingResults.push({
        order_id: order.id,
        selected_fulfillment_center: selectedFC?.fulfillmentCenter,
        total_score: selectedFC?.totalScore,
        score_breakdown: selectedFC?.breakdown,
        strategy: this.strategies.HYBRID,
        success: !!selectedFC
      });
    }

    return routingResults;
  }

  /**
   * Batch optimization for multiple orders
   */
  async optimizeBatchRouting(orders, fulfillmentCenters) {
    // Group orders by geographic region for potential consolidation
    const regions = this.groupOrdersByRegion(orders);
    const optimizedRouting = [];

    for (const [region, regionOrders] of Object.entries(regions)) {
      // Find optimal FC for this region
      const regionCenter = this.calculateRegionCenter(regionOrders);
      const nearestFCs = this.findNearestFulfillmentCenters(regionCenter, fulfillmentCenters, 3);

      // Optimize routing within region
      const regionRouting = await this.optimizeRegionRouting(regionOrders, nearestFCs);
      optimizedRouting.push(...regionRouting);
    }

    return optimizedRouting;
  }

  /**
   * Group orders by geographic region
   */
  groupOrdersByRegion(orders) {
    const regions = {};

    orders.forEach(order => {
      const state = order.shipping_address.state;
      const region = this.getRegionForState(state);
      
      if (!regions[region]) {
        regions[region] = [];
      }
      regions[region].push(order);
    });

    return regions;
  }

  /**
   * Get region for a state
   */
  getRegionForState(state) {
    const regions = {
      'Northeast': ['NY', 'NJ', 'PA', 'CT', 'MA', 'VT', 'NH', 'ME', 'RI'],
      'Southeast': ['FL', 'GA', 'SC', 'NC', 'VA', 'WV', 'KY', 'TN', 'AL', 'MS', 'AR', 'LA'],
      'Midwest': ['OH', 'IN', 'IL', 'MI', 'WI', 'MN', 'IA', 'MO', 'ND', 'SD', 'NE', 'KS'],
      'Southwest': ['TX', 'OK', 'NM', 'AZ'],
      'West': ['CA', 'NV', 'UT', 'CO', 'WY', 'MT', 'ID', 'WA', 'OR'],
      'Alaska': ['AK'],
      'Hawaii': ['HI']
    };

    for (const [region, states] of Object.entries(regions)) {
      if (states.includes(state)) {
        return region;
      }
    }

    return 'Other';
  }

  /**
   * Calculate center point of a region's orders
   */
  calculateRegionCenter(orders) {
    const coordinates = orders.map(order => 
      this.getAddressCoordinates(order.shipping_address)
    );

    const avgLat = coordinates.reduce((sum, coord) => sum + coord.latitude, 0) / coordinates.length;
    const avgLon = coordinates.reduce((sum, coord) => sum + coord.longitude, 0) / coordinates.length;

    return { latitude: avgLat, longitude: avgLon };
  }

  /**
   * Find nearest fulfillment centers to a point
   */
  findNearestFulfillmentCenters(point, fulfillmentCenters, count = 3) {
    const distances = fulfillmentCenters.map(fc => ({
      fulfillmentCenter: fc,
      distance: this.calculateDistance(
        this.parseCoordinates(fc.coordinates),
        point
      )
    }));

    return distances
      .sort((a, b) => a.distance - b.distance)
      .slice(0, count)
      .map(d => d.fulfillmentCenter);
  }

  /**
   * Optimize routing within a region
   */
  async optimizeRegionRouting(orders, fulfillmentCenters) {
    // Use hybrid routing for region optimization
    return await this.hybridRouting(orders, fulfillmentCenters);
  }

  /**
   * Helper methods
   */
  async canFulfillOrder(fulfillmentCenter, order) {
    // Simplified check - in real implementation, check actual inventory
    return true;
  }

  async calculateFulfillmentCost(fulfillmentCenter, order) {
    // Simplified cost calculation
    const baseCost = 5.0;
    const itemCost = order.items.length * 1.5;
    const distance = this.calculateDistance(
      this.parseCoordinates(fulfillmentCenter.coordinates),
      this.getAddressCoordinates(order.shipping_address)
    );
    const shippingCost = distance * 0.01;

    return baseCost + itemCost + shippingCost;
  }

  async calculateInventoryScore(fulfillmentCenter, order) {
    // Simplified inventory score - in real implementation, check actual inventory levels
    return Math.random() * 100; // 0-100 score
  }

  async calculateCapacityScore(fulfillmentCenter) {
    // Simplified capacity score
    return Math.random() * 100; // 0-100 score
  }

  estimateOrderCapacityUsage(order) {
    // Estimate how much capacity this order will use
    return order.items.reduce((sum, item) => sum + item.quantity, 0) * 0.1;
  }

  parseCoordinates(coordinatesPoint) {
    if (!coordinatesPoint) {
      return { latitude: 39.8283, longitude: -98.5795 }; // Center of US
    }

    const match = coordinatesPoint.match(/\(([^,]+),([^)]+)\)/);
    if (!match) {
      return { latitude: 39.8283, longitude: -98.5795 };
    }

    return {
      longitude: parseFloat(match[1]),
      latitude: parseFloat(match[2])
    };
  }

  getAddressCoordinates(address) {
    // Simplified coordinate lookup
    const cityCoordinates = {
      'new york': { latitude: 40.7128, longitude: -74.0060 },
      'los angeles': { latitude: 34.0522, longitude: -118.2437 },
      'chicago': { latitude: 41.8781, longitude: -87.6298 },
      'houston': { latitude: 29.7604, longitude: -95.3698 },
      'phoenix': { latitude: 33.4484, longitude: -112.0740 }
    };

    const city = address.city?.toLowerCase();
    return cityCoordinates[city] || { latitude: 39.8283, longitude: -98.5795 };
  }

  calculateDistance(coord1, coord2) {
    const R = 3959; // Earth's radius in miles
    
    const lat1Rad = coord1.latitude * Math.PI / 180;
    const lat2Rad = coord2.latitude * Math.PI / 180;
    const deltaLatRad = (coord2.latitude - coord1.latitude) * Math.PI / 180;
    const deltaLonRad = (coord2.longitude - coord1.longitude) * Math.PI / 180;

    const a = Math.sin(deltaLatRad / 2) * Math.sin(deltaLatRad / 2) +
              Math.cos(lat1Rad) * Math.cos(lat2Rad) *
              Math.sin(deltaLonRad / 2) * Math.sin(deltaLonRad / 2);
    
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
    
    return R * c;
  }
}

module.exports = new OrderRoutingAlgorithm();
