const { Pool } = require('pg');
const logger = require('../utils/logger');

let pool;

const dbConfig = {
  host: process.env.DB_HOST || 'localhost',
  port: process.env.DB_PORT || 5432,
  database: process.env.DB_NAME || 'orderflow_pro',
  user: process.env.DB_USER || 'postgres',
  password: process.env.DB_PASSWORD || 'password',
  max: parseInt(process.env.DB_POOL_MAX) || 20,
  idleTimeoutMillis: parseInt(process.env.DB_IDLE_TIMEOUT) || 30000,
  connectionTimeoutMillis: parseInt(process.env.DB_CONNECTION_TIMEOUT) || 2000,
  ssl: process.env.NODE_ENV === 'production' ? { rejectUnauthorized: false } : false
};

async function connectDatabase() {
  try {
    pool = new Pool(dbConfig);
    
    // Test the connection
    const client = await pool.connect();
    await client.query('SELECT NOW()');
    client.release();
    
    logger.info('Database connection established successfully');
    
    // Handle pool errors
    pool.on('error', (err) => {
      logger.error('Unexpected error on idle client', err);
    });
    
    return pool;
  } catch (error) {
    logger.error('Failed to connect to database:', error);
    throw error;
  }
}

async function query(text, params) {
  const start = Date.now();
  try {
    const result = await pool.query(text, params);
    const duration = Date.now() - start;
    
    if (duration > 1000) {
      logger.warn(`Slow query detected (${duration}ms):`, { query: text, params });
    }
    
    return result;
  } catch (error) {
    logger.error('Database query error:', { query: text, params, error: error.message });
    throw error;
  }
}

async function getClient() {
  return await pool.connect();
}

async function transaction(callback) {
  const client = await pool.connect();
  
  try {
    await client.query('BEGIN');
    const result = await callback(client);
    await client.query('COMMIT');
    return result;
  } catch (error) {
    await client.query('ROLLBACK');
    throw error;
  } finally {
    client.release();
  }
}

// Helper function for pagination
function buildPaginationQuery(baseQuery, page = 1, limit = 10, orderBy = 'created_at', orderDirection = 'DESC') {
  const offset = (page - 1) * limit;
  const validOrderDirections = ['ASC', 'DESC'];
  const direction = validOrderDirections.includes(orderDirection.toUpperCase()) ? orderDirection.toUpperCase() : 'DESC';
  
  return `
    ${baseQuery}
    ORDER BY ${orderBy} ${direction}
    LIMIT $${baseQuery.split('$').length} OFFSET $${baseQuery.split('$').length + 1}
  `;
}

// Helper function for search queries
function buildSearchQuery(searchTerm, searchFields) {
  if (!searchTerm || !searchFields.length) {
    return { whereClause: '', params: [] };
  }
  
  const searchConditions = searchFields.map((field, index) => 
    `${field} ILIKE $${index + 1}`
  ).join(' OR ');
  
  const params = searchFields.map(() => `%${searchTerm}%`);
  
  return {
    whereClause: `AND (${searchConditions})`,
    params
  };
}

// Database health check
async function healthCheck() {
  try {
    const result = await query('SELECT 1 as health_check');
    return {
      status: 'healthy',
      timestamp: new Date().toISOString(),
      connection_count: pool.totalCount,
      idle_count: pool.idleCount,
      waiting_count: pool.waitingCount
    };
  } catch (error) {
    return {
      status: 'unhealthy',
      error: error.message,
      timestamp: new Date().toISOString()
    };
  }
}

// Graceful shutdown
async function closeDatabase() {
  if (pool) {
    await pool.end();
    logger.info('Database pool closed');
  }
}

module.exports = {
  connectDatabase,
  query,
  getClient,
  transaction,
  buildPaginationQuery,
  buildSearchQuery,
  healthCheck,
  closeDatabase,
  pool: () => pool
};
