require('dotenv').config();

const config = {
  // Server configuration
  PORT: process.env.PORT || 3000,
  NODE_ENV: process.env.NODE_ENV || 'development',
  
  // Database configuration
  database: {
    host: process.env.DB_HOST || 'localhost',
    port: parseInt(process.env.DB_PORT) || 5432,
    name: process.env.DB_NAME || 'orderflow_pro',
    user: process.env.DB_USER || 'postgres',
    password: process.env.DB_PASSWORD || 'password',
    poolMax: parseInt(process.env.DB_POOL_MAX) || 20,
    idleTimeout: parseInt(process.env.DB_IDLE_TIMEOUT) || 30000,
    connectionTimeout: parseInt(process.env.DB_CONNECTION_TIMEOUT) || 2000,
    ssl: process.env.NODE_ENV === 'production'
  },
  
  // Redis configuration
  redis: {
    host: process.env.REDIS_HOST || 'localhost',
    port: parseInt(process.env.REDIS_PORT) || 6379,
    password: process.env.REDIS_PASSWORD || undefined,
    db: parseInt(process.env.REDIS_DB) || 0
  },
  
  // JWT configuration
  jwt: {
    secret: process.env.JWT_SECRET || 'your-super-secret-jwt-key-change-in-production',
    expiresIn: process.env.JWT_EXPIRES_IN || '24h',
    refreshExpiresIn: process.env.JWT_REFRESH_EXPIRES_IN || '7d'
  },
  
  // SendGrid configuration
  sendgrid: {
    apiKey: process.env.SENDGRID_API_KEY,
    fromEmail: process.env.SENDGRID_FROM_EMAIL || '<EMAIL>',
    fromName: process.env.SENDGRID_FROM_NAME || 'OrderFlow Pro'
  },
  
  // Twilio configuration
  twilio: {
    accountSid: process.env.TWILIO_ACCOUNT_SID,
    authToken: process.env.TWILIO_AUTH_TOKEN,
    fromNumber: process.env.TWILIO_FROM_NUMBER
  },
  
  // Shipping carriers configuration
  shipping: {
    ups: {
      apiKey: process.env.UPS_API_KEY,
      username: process.env.UPS_USERNAME,
      password: process.env.UPS_PASSWORD,
      sandbox: process.env.UPS_SANDBOX === 'true'
    },
    fedex: {
      apiKey: process.env.FEDEX_API_KEY,
      secretKey: process.env.FEDEX_SECRET_KEY,
      accountNumber: process.env.FEDEX_ACCOUNT_NUMBER,
      meterNumber: process.env.FEDEX_METER_NUMBER,
      sandbox: process.env.FEDEX_SANDBOX === 'true'
    },
    usps: {
      userId: process.env.USPS_USER_ID,
      password: process.env.USPS_PASSWORD,
      sandbox: process.env.USPS_SANDBOX === 'true'
    }
  },
  
  // Payment processors configuration
  payment: {
    stripe: {
      secretKey: process.env.STRIPE_SECRET_KEY,
      publishableKey: process.env.STRIPE_PUBLISHABLE_KEY,
      webhookSecret: process.env.STRIPE_WEBHOOK_SECRET
    },
    paypal: {
      clientId: process.env.PAYPAL_CLIENT_ID,
      clientSecret: process.env.PAYPAL_CLIENT_SECRET,
      sandbox: process.env.PAYPAL_SANDBOX === 'true'
    }
  },
  
  // File storage configuration
  storage: {
    type: process.env.STORAGE_TYPE || 'local', // 'local', 's3', 'gcs'
    local: {
      uploadPath: process.env.LOCAL_UPLOAD_PATH || './uploads'
    },
    s3: {
      accessKeyId: process.env.AWS_ACCESS_KEY_ID,
      secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY,
      region: process.env.AWS_REGION || 'us-east-1',
      bucket: process.env.AWS_S3_BUCKET
    }
  },
  
  // Rate limiting configuration
  rateLimit: {
    windowMs: parseInt(process.env.RATE_LIMIT_WINDOW_MS) || 15 * 60 * 1000, // 15 minutes
    max: parseInt(process.env.RATE_LIMIT_MAX) || 100, // limit each IP to 100 requests per windowMs
    skipSuccessfulRequests: process.env.RATE_LIMIT_SKIP_SUCCESSFUL === 'true'
  },
  
  // CORS configuration
  cors: {
    origin: process.env.ALLOWED_ORIGINS?.split(',') || ['http://localhost:3000', 'http://localhost:3001'],
    credentials: true
  },
  
  // Logging configuration
  logging: {
    level: process.env.LOG_LEVEL || 'info',
    file: process.env.LOG_FILE || './logs/app.log',
    maxSize: process.env.LOG_MAX_SIZE || '20m',
    maxFiles: parseInt(process.env.LOG_MAX_FILES) || 5
  },
  
  // Business logic configuration
  business: {
    // Inventory management
    lowStockThreshold: parseInt(process.env.LOW_STOCK_THRESHOLD) || 10,
    autoReorderEnabled: process.env.AUTO_REORDER_ENABLED === 'true',
    
    // Order processing
    orderTimeoutMinutes: parseInt(process.env.ORDER_TIMEOUT_MINUTES) || 30,
    maxOrderItems: parseInt(process.env.MAX_ORDER_ITEMS) || 100,
    
    // Shipping
    freeShippingThreshold: parseFloat(process.env.FREE_SHIPPING_THRESHOLD) || 50.00,
    defaultShippingCost: parseFloat(process.env.DEFAULT_SHIPPING_COST) || 9.99,
    
    // Returns
    returnWindowDays: parseInt(process.env.RETURN_WINDOW_DAYS) || 30,
    restockingFeePercent: parseFloat(process.env.RESTOCKING_FEE_PERCENT) || 0.15,
    
    // Notifications
    emailNotificationsEnabled: process.env.EMAIL_NOTIFICATIONS_ENABLED !== 'false',
    smsNotificationsEnabled: process.env.SMS_NOTIFICATIONS_ENABLED === 'true',
    
    // Analytics
    analyticsRetentionDays: parseInt(process.env.ANALYTICS_RETENTION_DAYS) || 365,
    realtimeAnalyticsEnabled: process.env.REALTIME_ANALYTICS_ENABLED !== 'false'
  },
  
  // Cache configuration
  cache: {
    defaultTtl: parseInt(process.env.CACHE_DEFAULT_TTL) || 3600, // 1 hour
    inventoryTtl: parseInt(process.env.CACHE_INVENTORY_TTL) || 300, // 5 minutes
    orderTtl: parseInt(process.env.CACHE_ORDER_TTL) || 1800, // 30 minutes
    analyticsTtl: parseInt(process.env.CACHE_ANALYTICS_TTL) || 3600 // 1 hour
  },
  
  // External API configuration
  externalApis: {
    timeout: parseInt(process.env.EXTERNAL_API_TIMEOUT) || 30000, // 30 seconds
    retries: parseInt(process.env.EXTERNAL_API_RETRIES) || 3,
    retryDelay: parseInt(process.env.EXTERNAL_API_RETRY_DELAY) || 1000 // 1 second
  }
};

// Validation
function validateConfig() {
  const required = [
    'JWT_SECRET',
    'DB_PASSWORD'
  ];
  
  const missing = required.filter(key => !process.env[key]);
  
  if (missing.length > 0) {
    throw new Error(`Missing required environment variables: ${missing.join(', ')}`);
  }
  
  // Warn about missing optional but recommended variables
  const recommended = [
    'SENDGRID_API_KEY',
    'TWILIO_ACCOUNT_SID',
    'TWILIO_AUTH_TOKEN'
  ];
  
  const missingRecommended = recommended.filter(key => !process.env[key]);
  
  if (missingRecommended.length > 0) {
    console.warn(`Warning: Missing recommended environment variables: ${missingRecommended.join(', ')}`);
  }
}

// Only validate in production
if (config.NODE_ENV === 'production') {
  validateConfig();
}

module.exports = config;
