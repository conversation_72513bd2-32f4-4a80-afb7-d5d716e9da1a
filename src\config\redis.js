const redis = require('redis');
const logger = require('../utils/logger');

let client;

const redisConfig = {
  host: process.env.REDIS_HOST || 'localhost',
  port: process.env.REDIS_PORT || 6379,
  password: process.env.REDIS_PASSWORD || undefined,
  db: process.env.REDIS_DB || 0,
  retryDelayOnFailover: 100,
  enableReadyCheck: true,
  maxRetriesPerRequest: 3,
  lazyConnect: true
};

async function connectRedis() {
  try {
    client = redis.createClient({
      socket: {
        host: redisConfig.host,
        port: redisConfig.port,
        reconnectStrategy: (retries) => {
          if (retries > 10) {
            logger.error('Redis reconnection failed after 10 attempts');
            return new Error('Redis reconnection failed');
          }
          return Math.min(retries * 50, 500);
        }
      },
      password: redisConfig.password,
      database: redisConfig.db
    });

    client.on('error', (err) => {
      logger.error('Redis Client Error:', err);
    });

    client.on('connect', () => {
      logger.info('Redis client connected');
    });

    client.on('ready', () => {
      logger.info('Redis client ready');
    });

    client.on('end', () => {
      logger.info('Redis client disconnected');
    });

    await client.connect();
    
    // Test the connection
    await client.ping();
    logger.info('Redis connection established successfully');
    
    return client;
  } catch (error) {
    logger.error('Failed to connect to Redis:', error);
    throw error;
  }
}

// Cache operations
async function get(key) {
  try {
    const value = await client.get(key);
    return value ? JSON.parse(value) : null;
  } catch (error) {
    logger.error(`Redis GET error for key ${key}:`, error);
    return null;
  }
}

async function set(key, value, ttl = 3600) {
  try {
    const serializedValue = JSON.stringify(value);
    if (ttl) {
      await client.setEx(key, ttl, serializedValue);
    } else {
      await client.set(key, serializedValue);
    }
    return true;
  } catch (error) {
    logger.error(`Redis SET error for key ${key}:`, error);
    return false;
  }
}

async function del(key) {
  try {
    await client.del(key);
    return true;
  } catch (error) {
    logger.error(`Redis DEL error for key ${key}:`, error);
    return false;
  }
}

async function exists(key) {
  try {
    const result = await client.exists(key);
    return result === 1;
  } catch (error) {
    logger.error(`Redis EXISTS error for key ${key}:`, error);
    return false;
  }
}

async function expire(key, ttl) {
  try {
    await client.expire(key, ttl);
    return true;
  } catch (error) {
    logger.error(`Redis EXPIRE error for key ${key}:`, error);
    return false;
  }
}

// Hash operations
async function hget(key, field) {
  try {
    const value = await client.hGet(key, field);
    return value ? JSON.parse(value) : null;
  } catch (error) {
    logger.error(`Redis HGET error for key ${key}, field ${field}:`, error);
    return null;
  }
}

async function hset(key, field, value) {
  try {
    const serializedValue = JSON.stringify(value);
    await client.hSet(key, field, serializedValue);
    return true;
  } catch (error) {
    logger.error(`Redis HSET error for key ${key}, field ${field}:`, error);
    return false;
  }
}

async function hgetall(key) {
  try {
    const hash = await client.hGetAll(key);
    const result = {};
    for (const [field, value] of Object.entries(hash)) {
      result[field] = JSON.parse(value);
    }
    return result;
  } catch (error) {
    logger.error(`Redis HGETALL error for key ${key}:`, error);
    return {};
  }
}

// List operations
async function lpush(key, value) {
  try {
    const serializedValue = JSON.stringify(value);
    await client.lPush(key, serializedValue);
    return true;
  } catch (error) {
    logger.error(`Redis LPUSH error for key ${key}:`, error);
    return false;
  }
}

async function rpop(key) {
  try {
    const value = await client.rPop(key);
    return value ? JSON.parse(value) : null;
  } catch (error) {
    logger.error(`Redis RPOP error for key ${key}:`, error);
    return null;
  }
}

// Cache patterns for common operations
const CACHE_KEYS = {
  INVENTORY: (productId, fcId) => `inventory:${productId}:${fcId}`,
  ORDER: (orderId) => `order:${orderId}`,
  CUSTOMER: (customerId) => `customer:${customerId}`,
  PRODUCT: (productId) => `product:${productId}`,
  ANALYTICS: (type, date) => `analytics:${type}:${date}`,
  SESSION: (sessionId) => `session:${sessionId}`,
  RATE_LIMIT: (ip) => `rate_limit:${ip}`
};

// Inventory caching
async function cacheInventory(productId, fulfillmentCenterId, inventory, ttl = 300) {
  const key = CACHE_KEYS.INVENTORY(productId, fulfillmentCenterId);
  return await set(key, inventory, ttl);
}

async function getCachedInventory(productId, fulfillmentCenterId) {
  const key = CACHE_KEYS.INVENTORY(productId, fulfillmentCenterId);
  return await get(key);
}

// Order caching
async function cacheOrder(orderId, order, ttl = 1800) {
  const key = CACHE_KEYS.ORDER(orderId);
  return await set(key, order, ttl);
}

async function getCachedOrder(orderId) {
  const key = CACHE_KEYS.ORDER(orderId);
  return await get(key);
}

// Analytics caching
async function cacheAnalytics(type, date, data, ttl = 3600) {
  const key = CACHE_KEYS.ANALYTICS(type, date);
  return await set(key, data, ttl);
}

async function getCachedAnalytics(type, date) {
  const key = CACHE_KEYS.ANALYTICS(type, date);
  return await get(key);
}

// Redis health check
async function healthCheck() {
  try {
    const start = Date.now();
    await client.ping();
    const latency = Date.now() - start;
    
    return {
      status: 'healthy',
      latency: `${latency}ms`,
      timestamp: new Date().toISOString()
    };
  } catch (error) {
    return {
      status: 'unhealthy',
      error: error.message,
      timestamp: new Date().toISOString()
    };
  }
}

// Graceful shutdown
async function closeRedis() {
  if (client) {
    await client.quit();
    logger.info('Redis connection closed');
  }
}

module.exports = {
  connectRedis,
  get,
  set,
  del,
  exists,
  expire,
  hget,
  hset,
  hgetall,
  lpush,
  rpop,
  CACHE_KEYS,
  cacheInventory,
  getCachedInventory,
  cacheOrder,
  getCachedOrder,
  cacheAnalytics,
  getCachedAnalytics,
  healthCheck,
  closeRedis,
  client: () => client
};
