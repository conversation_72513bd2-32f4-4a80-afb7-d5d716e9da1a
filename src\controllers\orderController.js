const orderService = require('../services/orderService');
const inventoryService = require('../services/inventoryService');
const notificationService = require('../services/notificationService');
const routingService = require('../services/routingService');
const logger = require('../utils/logger');
const { NotFoundError, ValidationError, ConflictError } = require('../middleware/errorHandler');

class OrderController {
  /**
   * Get all orders with filtering and pagination
   */
  async getOrders(req, res) {
    const {
      page = 1,
      limit = 20,
      status,
      payment_status,
      channel_id,
      customer_id,
      date_from,
      date_to,
      search
    } = req.query;

    const filters = {
      status,
      payment_status,
      channel_id,
      customer_id,
      date_from,
      date_to,
      search
    };

    const result = await orderService.getOrders(filters, {
      page: parseInt(page),
      limit: parseInt(limit)
    });

    logger.logBusinessOperation('get_orders', 'order', null, req.user?.id, {
      filters,
      pagination: { page, limit },
      resultCount: result.orders.length
    });

    res.json({
      success: true,
      data: result.orders,
      pagination: result.pagination
    });
  }

  /**
   * Get order by ID
   */
  async getOrderById(req, res) {
    const { id } = req.params;

    const order = await orderService.getOrderById(id);
    if (!order) {
      throw new NotFoundError('Order');
    }

    logger.logBusinessOperation('get_order', 'order', id, req.user?.id);

    res.json({
      success: true,
      data: order
    });
  }

  /**
   * Create new order
   */
  async createOrder(req, res) {
    const orderData = req.body;

    // Validate inventory availability
    for (const item of orderData.items) {
      const availability = await inventoryService.checkAvailability(
        item.product_id,
        item.quantity
      );
      
      if (!availability.available) {
        throw new ValidationError(
          `Insufficient inventory for product ${item.product_id}. Available: ${availability.quantity}, Requested: ${item.quantity}`
        );
      }
    }

    // Determine optimal fulfillment center
    const fulfillmentCenter = await routingService.selectOptimalFulfillmentCenter(
      orderData.items,
      orderData.shipping_address
    );

    orderData.fulfillment_center_id = fulfillmentCenter.id;

    // Create order
    const order = await orderService.createOrder(orderData);

    // Reserve inventory
    await inventoryService.reserveInventory(order.id, orderData.items);

    // Send order confirmation
    await notificationService.sendOrderConfirmation(order);

    logger.logOrder(order.id, 'order_created', {
      customer_id: order.customer_id,
      channel_id: order.channel_id,
      total_amount: order.total_amount,
      fulfillment_center_id: order.fulfillment_center_id
    });

    res.status(201).json({
      success: true,
      data: order,
      message: 'Order created successfully'
    });
  }

  /**
   * Update order
   */
  async updateOrder(req, res) {
    const { id } = req.params;
    const updates = req.body;

    const existingOrder = await orderService.getOrderById(id);
    if (!existingOrder) {
      throw new NotFoundError('Order');
    }

    // Validate status transitions
    if (updates.status && !orderService.isValidStatusTransition(existingOrder.status, updates.status)) {
      throw new ValidationError(`Invalid status transition from ${existingOrder.status} to ${updates.status}`);
    }

    const updatedOrder = await orderService.updateOrder(id, updates);

    // Handle status-specific logic
    if (updates.status) {
      await this.handleStatusChange(updatedOrder, existingOrder.status, updates.status);
    }

    logger.logOrder(id, 'order_updated', {
      updates,
      previous_status: existingOrder.status,
      new_status: updates.status,
      updated_by: req.user?.id
    });

    res.json({
      success: true,
      data: updatedOrder,
      message: 'Order updated successfully'
    });
  }

  /**
   * Cancel order
   */
  async cancelOrder(req, res) {
    const { id } = req.params;

    const order = await orderService.getOrderById(id);
    if (!order) {
      throw new NotFoundError('Order');
    }

    if (!['pending', 'confirmed'].includes(order.status)) {
      throw new ConflictError('Order cannot be cancelled in current status');
    }

    // Release reserved inventory
    await inventoryService.releaseReservedInventory(id);

    // Update order status
    const cancelledOrder = await orderService.updateOrder(id, {
      status: 'cancelled',
      updated_at: new Date()
    });

    // Send cancellation notification
    await notificationService.sendOrderCancellation(cancelledOrder);

    logger.logOrder(id, 'order_cancelled', {
      cancelled_by: req.user?.id,
      reason: 'manual_cancellation'
    });

    res.json({
      success: true,
      data: cancelledOrder,
      message: 'Order cancelled successfully'
    });
  }

  /**
   * Process order (move to next stage)
   */
  async processOrder(req, res) {
    const { id } = req.params;

    const order = await orderService.getOrderById(id);
    if (!order) {
      throw new NotFoundError('Order');
    }

    const nextStatus = orderService.getNextStatus(order.status);
    if (!nextStatus) {
      throw new ConflictError('Order cannot be processed further');
    }

    const processedOrder = await orderService.processOrder(id);

    logger.logOrder(id, 'order_processed', {
      previous_status: order.status,
      new_status: processedOrder.status,
      processed_by: req.user?.id
    });

    res.json({
      success: true,
      data: processedOrder,
      message: 'Order processed successfully'
    });
  }

  /**
   * Mark order as shipped
   */
  async shipOrder(req, res) {
    const { id } = req.params;
    const { tracking_number, shipping_method } = req.body;

    const order = await orderService.getOrderById(id);
    if (!order) {
      throw new NotFoundError('Order');
    }

    if (order.status !== 'processing') {
      throw new ConflictError('Order must be in processing status to ship');
    }

    const shippedOrder = await orderService.updateOrder(id, {
      status: 'shipped',
      tracking_number,
      shipping_method,
      updated_at: new Date()
    });

    // Update inventory (move from reserved to sold)
    await inventoryService.fulfillOrder(id);

    // Send shipping notification
    await notificationService.sendShippingNotification(shippedOrder);

    logger.logOrder(id, 'order_shipped', {
      tracking_number,
      shipping_method,
      shipped_by: req.user?.id
    });

    res.json({
      success: true,
      data: shippedOrder,
      message: 'Order shipped successfully'
    });
  }

  /**
   * Mark order as delivered
   */
  async deliverOrder(req, res) {
    const { id } = req.params;

    const order = await orderService.getOrderById(id);
    if (!order) {
      throw new NotFoundError('Order');
    }

    if (order.status !== 'shipped') {
      throw new ConflictError('Order must be in shipped status to deliver');
    }

    const deliveredOrder = await orderService.updateOrder(id, {
      status: 'delivered',
      updated_at: new Date()
    });

    // Send delivery confirmation
    await notificationService.sendDeliveryConfirmation(deliveredOrder);

    logger.logOrder(id, 'order_delivered', {
      delivered_by: req.user?.id
    });

    res.json({
      success: true,
      data: deliveredOrder,
      message: 'Order marked as delivered'
    });
  }

  /**
   * Get order tracking information
   */
  async getOrderTracking(req, res) {
    const { id } = req.params;

    const tracking = await orderService.getOrderTracking(id);
    if (!tracking) {
      throw new NotFoundError('Order tracking information');
    }

    res.json({
      success: true,
      data: tracking
    });
  }

  /**
   * Get orders for specific customer
   */
  async getOrdersByCustomer(req, res) {
    const { customerId } = req.params;
    const { page = 1, limit = 20 } = req.query;

    const result = await orderService.getOrdersByCustomer(customerId, {
      page: parseInt(page),
      limit: parseInt(limit)
    });

    res.json({
      success: true,
      data: result.orders,
      pagination: result.pagination
    });
  }

  /**
   * Get orders for specific channel
   */
  async getOrdersByChannel(req, res) {
    const { channelId } = req.params;
    const { page = 1, limit = 20 } = req.query;

    const result = await orderService.getOrdersByChannel(channelId, {
      page: parseInt(page),
      limit: parseInt(limit)
    });

    res.json({
      success: true,
      data: result.orders,
      pagination: result.pagination
    });
  }

  /**
   * Bulk update orders
   */
  async bulkUpdateOrders(req, res) {
    const { order_ids, updates } = req.body;

    const result = await orderService.bulkUpdateOrders(order_ids, updates);

    logger.logBusinessOperation('bulk_update_orders', 'order', null, req.user?.id, {
      order_count: order_ids.length,
      updates
    });

    res.json({
      success: true,
      data: result,
      message: `${result.updated_count} orders updated successfully`
    });
  }

  /**
   * Export orders to CSV
   */
  async exportOrders(req, res) {
    const filters = req.query;
    
    const csvData = await orderService.exportOrdersToCSV(filters);

    res.setHeader('Content-Type', 'text/csv');
    res.setHeader('Content-Disposition', 'attachment; filename=orders.csv');
    res.send(csvData);
  }

  /**
   * Handle webhook from external channels
   */
  async handleWebhook(req, res) {
    const { channelId } = req.params;
    const webhookData = req.body;

    // Validate webhook signature/API key here
    
    const order = await orderService.processWebhookOrder(channelId, webhookData);

    logger.logBusinessOperation('webhook_order_received', 'order', order.id, null, {
      channel_id: channelId,
      webhook_data: webhookData
    });

    res.json({
      success: true,
      data: order,
      message: 'Webhook processed successfully'
    });
  }

  /**
   * Handle status change logic
   */
  async handleStatusChange(order, previousStatus, newStatus) {
    switch (newStatus) {
      case 'confirmed':
        await notificationService.sendOrderConfirmation(order);
        break;
      case 'processing':
        // Trigger fulfillment process
        break;
      case 'shipped':
        await notificationService.sendShippingNotification(order);
        break;
      case 'delivered':
        await notificationService.sendDeliveryConfirmation(order);
        break;
      case 'cancelled':
        await notificationService.sendOrderCancellation(order);
        await inventoryService.releaseReservedInventory(order.id);
        break;
    }
  }
}

module.exports = new OrderController();
