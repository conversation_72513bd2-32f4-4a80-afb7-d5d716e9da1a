const axios = require('axios');
const logger = require('../utils/logger');
const config = require('../config/environment');

/**
 * Base Channel Integration Class
 */
class BaseChannelIntegration {
  constructor(channelConfig) {
    this.config = channelConfig;
    this.apiClient = axios.create({
      timeout: config.externalApis.timeout,
      headers: {
        'Content-Type': 'application/json',
        'User-Agent': 'OrderFlow-Pro/1.0'
      }
    });

    // Add request/response interceptors
    this.setupInterceptors();
  }

  setupInterceptors() {
    this.apiClient.interceptors.request.use(
      (config) => {
        logger.logExternalApi(
          this.config.name,
          config.url,
          config.method.toUpperCase(),
          null,
          null
        );
        return config;
      },
      (error) => {
        logger.error('API Request Error:', error);
        return Promise.reject(error);
      }
    );

    this.apiClient.interceptors.response.use(
      (response) => {
        logger.logExternalApi(
          this.config.name,
          response.config.url,
          response.config.method.toUpperCase(),
          response.status,
          Date.now() - response.config.metadata?.startTime || 0
        );
        return response;
      },
      (error) => {
        const duration = Date.now() - error.config?.metadata?.startTime || 0;
        logger.logExternalApi(
          this.config.name,
          error.config?.url,
          error.config?.method?.toUpperCase(),
          error.response?.status,
          duration,
          error
        );
        return Promise.reject(error);
      }
    );
  }

  async makeRequest(method, endpoint, data = null, headers = {}) {
    const startTime = Date.now();
    
    try {
      const response = await this.apiClient({
        method,
        url: endpoint,
        data,
        headers: { ...this.getAuthHeaders(), ...headers },
        metadata: { startTime }
      });

      return response.data;
    } catch (error) {
      throw this.handleApiError(error);
    }
  }

  getAuthHeaders() {
    // Override in subclasses
    return {};
  }

  handleApiError(error) {
    if (error.response) {
      // Server responded with error status
      return new Error(`API Error: ${error.response.status} - ${error.response.data?.message || error.message}`);
    } else if (error.request) {
      // Request was made but no response received
      return new Error('API Timeout: No response received');
    } else {
      // Something else happened
      return new Error(`API Error: ${error.message}`);
    }
  }

  // Abstract methods to be implemented by subclasses
  async fetchOrders(params = {}) {
    throw new Error('fetchOrders method must be implemented');
  }

  async createOrder(orderData) {
    throw new Error('createOrder method must be implemented');
  }

  async updateOrder(orderId, updates) {
    throw new Error('updateOrder method must be implemented');
  }

  async getOrderStatus(orderId) {
    throw new Error('getOrderStatus method must be implemented');
  }

  async syncInventory(products) {
    throw new Error('syncInventory method must be implemented');
  }
}

/**
 * Shopify Integration
 */
class ShopifyIntegration extends BaseChannelIntegration {
  constructor(config) {
    super(config);
    this.baseUrl = `https://${config.shop_domain}.myshopify.com/admin/api/2023-10`;
  }

  getAuthHeaders() {
    return {
      'X-Shopify-Access-Token': this.config.access_token
    };
  }

  async fetchOrders(params = {}) {
    const queryParams = new URLSearchParams({
      status: params.status || 'any',
      limit: params.limit || 50,
      created_at_min: params.created_at_min,
      created_at_max: params.created_at_max,
      ...params
    });

    const response = await this.makeRequest(
      'GET',
      `${this.baseUrl}/orders.json?${queryParams}`
    );

    return this.transformShopifyOrders(response.orders);
  }

  async createOrder(orderData) {
    const shopifyOrder = this.transformToShopifyFormat(orderData);
    
    const response = await this.makeRequest(
      'POST',
      `${this.baseUrl}/orders.json`,
      { order: shopifyOrder }
    );

    return this.transformShopifyOrder(response.order);
  }

  async updateOrder(orderId, updates) {
    const shopifyUpdates = this.transformUpdatesToShopifyFormat(updates);
    
    const response = await this.makeRequest(
      'PUT',
      `${this.baseUrl}/orders/${orderId}.json`,
      { order: shopifyUpdates }
    );

    return this.transformShopifyOrder(response.order);
  }

  async getOrderStatus(orderId) {
    const response = await this.makeRequest(
      'GET',
      `${this.baseUrl}/orders/${orderId}.json`
    );

    return {
      id: response.order.id,
      status: response.order.financial_status,
      fulfillment_status: response.order.fulfillment_status
    };
  }

  async syncInventory(products) {
    const results = [];

    for (const product of products) {
      try {
        const response = await this.makeRequest(
          'PUT',
          `${this.baseUrl}/inventory_levels/set.json`,
          {
            location_id: this.config.location_id,
            inventory_item_id: product.shopify_inventory_item_id,
            available: product.quantity
          }
        );

        results.push({
          product_id: product.id,
          success: true,
          shopify_response: response
        });
      } catch (error) {
        results.push({
          product_id: product.id,
          success: false,
          error: error.message
        });
      }
    }

    return results;
  }

  transformShopifyOrders(shopifyOrders) {
    return shopifyOrders.map(order => this.transformShopifyOrder(order));
  }

  transformShopifyOrder(shopifyOrder) {
    return {
      external_id: shopifyOrder.id.toString(),
      external_order_number: shopifyOrder.order_number,
      customer_email: shopifyOrder.email,
      status: this.mapShopifyStatus(shopifyOrder.financial_status),
      total_amount: parseFloat(shopifyOrder.total_price),
      currency: shopifyOrder.currency,
      created_at: shopifyOrder.created_at,
      items: shopifyOrder.line_items.map(item => ({
        external_id: item.id.toString(),
        product_sku: item.sku,
        quantity: item.quantity,
        unit_price: parseFloat(item.price),
        total_price: parseFloat(item.price) * item.quantity
      })),
      billing_address: this.transformShopifyAddress(shopifyOrder.billing_address),
      shipping_address: this.transformShopifyAddress(shopifyOrder.shipping_address)
    };
  }

  transformToShopifyFormat(orderData) {
    return {
      email: orderData.customer_email,
      line_items: orderData.items.map(item => ({
        variant_id: item.shopify_variant_id,
        quantity: item.quantity,
        price: item.unit_price
      })),
      billing_address: this.transformAddressToShopify(orderData.billing_address),
      shipping_address: this.transformAddressToShopify(orderData.shipping_address),
      financial_status: 'pending',
      send_receipt: true,
      send_fulfillment_receipt: true
    };
  }

  transformShopifyAddress(shopifyAddress) {
    if (!shopifyAddress) return null;

    return {
      first_name: shopifyAddress.first_name,
      last_name: shopifyAddress.last_name,
      street_address: `${shopifyAddress.address1} ${shopifyAddress.address2 || ''}`.trim(),
      city: shopifyAddress.city,
      state: shopifyAddress.province,
      postal_code: shopifyAddress.zip,
      country: shopifyAddress.country,
      phone: shopifyAddress.phone
    };
  }

  transformAddressToShopify(address) {
    return {
      first_name: address.first_name,
      last_name: address.last_name,
      address1: address.street_address,
      city: address.city,
      province: address.state,
      zip: address.postal_code,
      country: address.country,
      phone: address.phone
    };
  }

  mapShopifyStatus(shopifyStatus) {
    const statusMap = {
      'pending': 'pending',
      'authorized': 'confirmed',
      'paid': 'processing',
      'partially_paid': 'processing',
      'refunded': 'returned',
      'voided': 'cancelled',
      'partially_refunded': 'processing'
    };

    return statusMap[shopifyStatus] || 'pending';
  }

  transformUpdatesToShopifyFormat(updates) {
    const shopifyUpdates = {};

    if (updates.status) {
      shopifyUpdates.financial_status = this.mapStatusToShopify(updates.status);
    }

    if (updates.tracking_number) {
      shopifyUpdates.tracking_number = updates.tracking_number;
    }

    return shopifyUpdates;
  }

  mapStatusToShopify(status) {
    const statusMap = {
      'pending': 'pending',
      'confirmed': 'authorized',
      'processing': 'paid',
      'shipped': 'paid',
      'delivered': 'paid',
      'cancelled': 'voided',
      'returned': 'refunded'
    };

    return statusMap[status] || 'pending';
  }
}

/**
 * Amazon Marketplace Integration
 */
class AmazonIntegration extends BaseChannelIntegration {
  constructor(config) {
    super(config);
    this.baseUrl = 'https://sellingpartnerapi-na.amazon.com';
  }

  getAuthHeaders() {
    // Amazon SP-API requires complex authentication
    // This is a simplified version - real implementation would need AWS Signature V4
    return {
      'Authorization': `Bearer ${this.config.access_token}`,
      'x-amz-access-token': this.config.access_token
    };
  }

  async fetchOrders(params = {}) {
    const queryParams = new URLSearchParams({
      MarketplaceIds: this.config.marketplace_id,
      CreatedAfter: params.created_after || new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString(),
      OrderStatuses: params.statuses || 'Unshipped,PartiallyShipped,Shipped',
      MaxResultsPerPage: params.limit || 50
    });

    const response = await this.makeRequest(
      'GET',
      `${this.baseUrl}/orders/v0/orders?${queryParams}`
    );

    return this.transformAmazonOrders(response.payload.Orders);
  }

  transformAmazonOrders(amazonOrders) {
    return amazonOrders.map(order => ({
      external_id: order.AmazonOrderId,
      external_order_number: order.AmazonOrderId,
      customer_email: order.BuyerEmail || '<EMAIL>',
      status: this.mapAmazonStatus(order.OrderStatus),
      total_amount: parseFloat(order.OrderTotal?.Amount || 0),
      currency: order.OrderTotal?.CurrencyCode || 'USD',
      created_at: order.PurchaseDate,
      shipping_address: this.transformAmazonAddress(order.ShippingAddress)
    }));
  }

  mapAmazonStatus(amazonStatus) {
    const statusMap = {
      'Pending': 'pending',
      'Unshipped': 'confirmed',
      'PartiallyShipped': 'processing',
      'Shipped': 'shipped',
      'Canceled': 'cancelled',
      'Unfulfillable': 'cancelled'
    };

    return statusMap[amazonStatus] || 'pending';
  }

  transformAmazonAddress(amazonAddress) {
    if (!amazonAddress) return null;

    return {
      first_name: amazonAddress.Name?.split(' ')[0] || '',
      last_name: amazonAddress.Name?.split(' ').slice(1).join(' ') || '',
      street_address: `${amazonAddress.AddressLine1 || ''} ${amazonAddress.AddressLine2 || ''}`.trim(),
      city: amazonAddress.City,
      state: amazonAddress.StateOrRegion,
      postal_code: amazonAddress.PostalCode,
      country: amazonAddress.CountryCode
    };
  }
}

/**
 * eBay Integration
 */
class EbayIntegration extends BaseChannelIntegration {
  constructor(config) {
    super(config);
    this.baseUrl = config.sandbox ? 
      'https://api.sandbox.ebay.com' : 
      'https://api.ebay.com';
  }

  getAuthHeaders() {
    return {
      'Authorization': `Bearer ${this.config.access_token}`,
      'Content-Language': 'en-US'
    };
  }

  async fetchOrders(params = {}) {
    const queryParams = new URLSearchParams({
      filter: `creationdate:[${params.created_after || new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString()}..${new Date().toISOString()}]`,
      limit: params.limit || 50
    });

    const response = await this.makeRequest(
      'GET',
      `${this.baseUrl}/sell/fulfillment/v1/order?${queryParams}`
    );

    return this.transformEbayOrders(response.orders);
  }

  transformEbayOrders(ebayOrders) {
    return ebayOrders.map(order => ({
      external_id: order.orderId,
      external_order_number: order.legacyOrderId,
      customer_email: order.buyer?.username + '@ebay.com',
      status: this.mapEbayStatus(order.orderFulfillmentStatus),
      total_amount: parseFloat(order.pricingSummary?.total?.value || 0),
      currency: order.pricingSummary?.total?.currency || 'USD',
      created_at: order.creationDate,
      shipping_address: this.transformEbayAddress(order.fulfillmentStartInstructions?.[0]?.shippingStep?.shipTo)
    }));
  }

  mapEbayStatus(ebayStatus) {
    const statusMap = {
      'NOT_STARTED': 'pending',
      'IN_PROGRESS': 'processing',
      'FULFILLED': 'shipped'
    };

    return statusMap[ebayStatus] || 'pending';
  }

  transformEbayAddress(ebayAddress) {
    if (!ebayAddress) return null;

    return {
      first_name: ebayAddress.contactAddress?.fullName?.split(' ')[0] || '',
      last_name: ebayAddress.contactAddress?.fullName?.split(' ').slice(1).join(' ') || '',
      street_address: ebayAddress.contactAddress?.addressLine1,
      city: ebayAddress.contactAddress?.city,
      state: ebayAddress.contactAddress?.stateOrProvince,
      postal_code: ebayAddress.contactAddress?.postalCode,
      country: ebayAddress.contactAddress?.countryCode
    };
  }
}

/**
 * Channel Integration Factory
 */
class ChannelIntegrationFactory {
  static create(channelType, config) {
    switch (channelType.toLowerCase()) {
      case 'shopify':
        return new ShopifyIntegration(config);
      case 'amazon':
        return new AmazonIntegration(config);
      case 'ebay':
        return new EbayIntegration(config);
      default:
        throw new Error(`Unsupported channel type: ${channelType}`);
    }
  }

  static getSupportedChannels() {
    return ['shopify', 'amazon', 'ebay'];
  }
}

module.exports = {
  BaseChannelIntegration,
  ShopifyIntegration,
  AmazonIntegration,
  EbayIntegration,
  ChannelIntegrationFactory
};
