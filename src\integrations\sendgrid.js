const sgMail = require('@sendgrid/mail');
const logger = require('../utils/logger');
const config = require('../config/environment');

class SendGridService {
  constructor() {
    this.isInitialized = false;
    this.fromEmail = config.sendgrid.fromEmail;
    this.fromName = config.sendgrid.fromName;
    
    this.initialize();
  }

  /**
   * Initialize SendGrid service
   */
  initialize() {
    try {
      if (!config.sendgrid.apiKey) {
        logger.warn('SendGrid API key not configured. Email notifications will be disabled.');
        return;
      }

      sgMail.setApiKey(config.sendgrid.apiKey);
      this.isInitialized = true;
      
      logger.info('SendGrid service initialized successfully');
    } catch (error) {
      logger.error('Failed to initialize SendGrid service:', error);
    }
  }

  /**
   * Send single email
   */
  async sendEmail({ to, subject, html, text, attachments = [] }) {
    if (!this.isInitialized) {
      logger.warn('SendGrid not initialized. Email not sent.');
      return { success: false, error: 'SendGrid not initialized' };
    }

    try {
      const msg = {
        to,
        from: {
          email: this.fromEmail,
          name: this.fromName
        },
        subject,
        html,
        text: text || this.stripHtml(html),
        attachments: attachments.map(att => ({
          content: att.content,
          filename: att.filename,
          type: att.type || 'application/octet-stream',
          disposition: att.disposition || 'attachment'
        }))
      };

      const result = await sgMail.send(msg);
      
      logger.info('Email sent successfully', {
        to,
        subject,
        messageId: result[0].headers['x-message-id']
      });

      return {
        success: true,
        messageId: result[0].headers['x-message-id'],
        statusCode: result[0].statusCode
      };

    } catch (error) {
      logger.error('Failed to send email:', {
        to,
        subject,
        error: error.message,
        code: error.code
      });

      return {
        success: false,
        error: error.message,
        code: error.code
      };
    }
  }

  /**
   * Send multiple emails
   */
  async sendMultipleEmails(emails) {
    if (!this.isInitialized) {
      logger.warn('SendGrid not initialized. Emails not sent.');
      return { success: false, error: 'SendGrid not initialized' };
    }

    try {
      const messages = emails.map(email => ({
        to: email.to,
        from: {
          email: this.fromEmail,
          name: this.fromName
        },
        subject: email.subject,
        html: email.html,
        text: email.text || this.stripHtml(email.html),
        attachments: email.attachments?.map(att => ({
          content: att.content,
          filename: att.filename,
          type: att.type || 'application/octet-stream',
          disposition: att.disposition || 'attachment'
        })) || []
      }));

      const results = await sgMail.send(messages);
      
      logger.info('Multiple emails sent successfully', {
        count: emails.length,
        results: results.map(r => ({
          messageId: r.headers['x-message-id'],
          statusCode: r.statusCode
        }))
      });

      return {
        success: true,
        results: results.map(r => ({
          messageId: r.headers['x-message-id'],
          statusCode: r.statusCode
        }))
      };

    } catch (error) {
      logger.error('Failed to send multiple emails:', {
        count: emails.length,
        error: error.message,
        code: error.code
      });

      return {
        success: false,
        error: error.message,
        code: error.code
      };
    }
  }

  /**
   * Send email using template
   */
  async sendTemplateEmail({ to, templateId, dynamicTemplateData, attachments = [] }) {
    if (!this.isInitialized) {
      logger.warn('SendGrid not initialized. Template email not sent.');
      return { success: false, error: 'SendGrid not initialized' };
    }

    try {
      const msg = {
        to,
        from: {
          email: this.fromEmail,
          name: this.fromName
        },
        templateId,
        dynamicTemplateData,
        attachments: attachments.map(att => ({
          content: att.content,
          filename: att.filename,
          type: att.type || 'application/octet-stream',
          disposition: att.disposition || 'attachment'
        }))
      };

      const result = await sgMail.send(msg);
      
      logger.info('Template email sent successfully', {
        to,
        templateId,
        messageId: result[0].headers['x-message-id']
      });

      return {
        success: true,
        messageId: result[0].headers['x-message-id'],
        statusCode: result[0].statusCode
      };

    } catch (error) {
      logger.error('Failed to send template email:', {
        to,
        templateId,
        error: error.message,
        code: error.code
      });

      return {
        success: false,
        error: error.message,
        code: error.code
      };
    }
  }

  /**
   * Send email with personalization
   */
  async sendPersonalizedEmail({ personalizations, subject, html, text, attachments = [] }) {
    if (!this.isInitialized) {
      logger.warn('SendGrid not initialized. Personalized email not sent.');
      return { success: false, error: 'SendGrid not initialized' };
    }

    try {
      const msg = {
        personalizations: personalizations.map(p => ({
          to: Array.isArray(p.to) ? p.to : [{ email: p.to }],
          subject: p.subject || subject,
          substitutions: p.substitutions || {},
          customArgs: p.customArgs || {}
        })),
        from: {
          email: this.fromEmail,
          name: this.fromName
        },
        subject,
        html,
        text: text || this.stripHtml(html),
        attachments: attachments.map(att => ({
          content: att.content,
          filename: att.filename,
          type: att.type || 'application/octet-stream',
          disposition: att.disposition || 'attachment'
        }))
      };

      const result = await sgMail.send(msg);
      
      logger.info('Personalized email sent successfully', {
        personalizationCount: personalizations.length,
        messageId: result[0].headers['x-message-id']
      });

      return {
        success: true,
        messageId: result[0].headers['x-message-id'],
        statusCode: result[0].statusCode
      };

    } catch (error) {
      logger.error('Failed to send personalized email:', {
        personalizationCount: personalizations.length,
        error: error.message,
        code: error.code
      });

      return {
        success: false,
        error: error.message,
        code: error.code
      };
    }
  }

  /**
   * Validate email address
   */
  validateEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }

  /**
   * Strip HTML tags from text
   */
  stripHtml(html) {
    if (!html) return '';
    return html.replace(/<[^>]*>/g, '').replace(/\s+/g, ' ').trim();
  }

  /**
   * Create email template helpers
   */
  createOrderConfirmationEmail(orderData) {
    return {
      subject: `Order Confirmation - ${orderData.orderNumber}`,
      html: this.generateOrderConfirmationHTML(orderData),
      text: this.generateOrderConfirmationText(orderData)
    };
  }

  createShippingNotificationEmail(orderData) {
    return {
      subject: `Your Order Has Shipped - ${orderData.orderNumber}`,
      html: this.generateShippingNotificationHTML(orderData),
      text: this.generateShippingNotificationText(orderData)
    };
  }

  createDeliveryConfirmationEmail(orderData) {
    return {
      subject: `Order Delivered - ${orderData.orderNumber}`,
      html: this.generateDeliveryConfirmationHTML(orderData),
      text: this.generateDeliveryConfirmationText(orderData)
    };
  }

  /**
   * HTML template generators
   */
  generateOrderConfirmationHTML(data) {
    return `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <title>Order Confirmation</title>
        <style>
          body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
          .container { max-width: 600px; margin: 0 auto; padding: 20px; }
          .header { background-color: #f8f9fa; padding: 20px; text-align: center; }
          .content { padding: 20px; }
          .order-details { background-color: #f8f9fa; padding: 15px; margin: 20px 0; }
          .footer { background-color: #343a40; color: white; padding: 20px; text-align: center; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>Order Confirmation</h1>
          </div>
          <div class="content">
            <p>Dear ${data.customerName},</p>
            <p>Thank you for your order! We're excited to confirm that we've received your order and it's being processed.</p>
            
            <div class="order-details">
              <h3>Order Details</h3>
              <p><strong>Order Number:</strong> ${data.orderNumber}</p>
              <p><strong>Order Date:</strong> ${data.orderDate}</p>
              <p><strong>Total Amount:</strong> $${data.totalAmount}</p>
              <p><strong>Estimated Delivery:</strong> ${data.estimatedDelivery}</p>
            </div>
            
            <p>We'll send you another email with tracking information once your order ships.</p>
            <p>If you have any questions, please don't hesitate to contact our customer service team.</p>
          </div>
          <div class="footer">
            <p>&copy; 2024 OrderFlow Pro. All rights reserved.</p>
          </div>
        </div>
      </body>
      </html>
    `;
  }

  generateOrderConfirmationText(data) {
    return `
      Order Confirmation
      
      Dear ${data.customerName},
      
      Thank you for your order! We're excited to confirm that we've received your order and it's being processed.
      
      Order Details:
      Order Number: ${data.orderNumber}
      Order Date: ${data.orderDate}
      Total Amount: $${data.totalAmount}
      Estimated Delivery: ${data.estimatedDelivery}
      
      We'll send you another email with tracking information once your order ships.
      
      If you have any questions, please don't hesitate to contact our customer service team.
      
      © 2024 OrderFlow Pro. All rights reserved.
    `;
  }

  generateShippingNotificationHTML(data) {
    return `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <title>Your Order Has Shipped</title>
        <style>
          body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
          .container { max-width: 600px; margin: 0 auto; padding: 20px; }
          .header { background-color: #28a745; color: white; padding: 20px; text-align: center; }
          .content { padding: 20px; }
          .tracking-info { background-color: #e9ecef; padding: 15px; margin: 20px 0; }
          .track-button { background-color: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; display: inline-block; margin: 10px 0; }
          .footer { background-color: #343a40; color: white; padding: 20px; text-align: center; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>Your Order Has Shipped!</h1>
          </div>
          <div class="content">
            <p>Dear ${data.customerName},</p>
            <p>Great news! Your order has been shipped and is on its way to you.</p>
            
            <div class="tracking-info">
              <h3>Shipping Information</h3>
              <p><strong>Order Number:</strong> ${data.orderNumber}</p>
              <p><strong>Tracking Number:</strong> ${data.trackingNumber}</p>
              <p><strong>Shipping Method:</strong> ${data.shippingMethod}</p>
              <p><strong>Estimated Delivery:</strong> ${data.estimatedDelivery}</p>
              
              <a href="${data.trackingUrl}" class="track-button">Track Your Package</a>
            </div>
            
            <p>You can track your package using the tracking number above or by clicking the track button.</p>
          </div>
          <div class="footer">
            <p>&copy; 2024 OrderFlow Pro. All rights reserved.</p>
          </div>
        </div>
      </body>
      </html>
    `;
  }

  generateShippingNotificationText(data) {
    return `
      Your Order Has Shipped!
      
      Dear ${data.customerName},
      
      Great news! Your order has been shipped and is on its way to you.
      
      Shipping Information:
      Order Number: ${data.orderNumber}
      Tracking Number: ${data.trackingNumber}
      Shipping Method: ${data.shippingMethod}
      Estimated Delivery: ${data.estimatedDelivery}
      
      You can track your package using the tracking number above.
      Tracking URL: ${data.trackingUrl}
      
      © 2024 OrderFlow Pro. All rights reserved.
    `;
  }

  generateDeliveryConfirmationHTML(data) {
    return `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <title>Order Delivered</title>
        <style>
          body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
          .container { max-width: 600px; margin: 0 auto; padding: 20px; }
          .header { background-color: #28a745; color: white; padding: 20px; text-align: center; }
          .content { padding: 20px; }
          .delivery-info { background-color: #d4edda; padding: 15px; margin: 20px 0; border-left: 4px solid #28a745; }
          .footer { background-color: #343a40; color: white; padding: 20px; text-align: center; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>Order Delivered!</h1>
          </div>
          <div class="content">
            <p>Dear ${data.customerName},</p>
            <p>Your order has been successfully delivered!</p>
            
            <div class="delivery-info">
              <h3>Delivery Confirmation</h3>
              <p><strong>Order Number:</strong> ${data.orderNumber}</p>
              <p><strong>Delivery Date:</strong> ${data.deliveryDate}</p>
            </div>
            
            <p>We hope you're happy with your purchase! If you have any questions or concerns, please don't hesitate to contact us.</p>
            <p>Thank you for choosing OrderFlow Pro!</p>
          </div>
          <div class="footer">
            <p>&copy; 2024 OrderFlow Pro. All rights reserved.</p>
          </div>
        </div>
      </body>
      </html>
    `;
  }

  generateDeliveryConfirmationText(data) {
    return `
      Order Delivered!
      
      Dear ${data.customerName},
      
      Your order has been successfully delivered!
      
      Delivery Confirmation:
      Order Number: ${data.orderNumber}
      Delivery Date: ${data.deliveryDate}
      
      We hope you're happy with your purchase! If you have any questions or concerns, please don't hesitate to contact us.
      
      Thank you for choosing OrderFlow Pro!
      
      © 2024 OrderFlow Pro. All rights reserved.
    `;
  }

  /**
   * Get service status
   */
  getStatus() {
    return {
      initialized: this.isInitialized,
      fromEmail: this.fromEmail,
      fromName: this.fromName,
      hasApiKey: !!config.sendgrid.apiKey
    };
  }
}

module.exports = new SendGridService();
