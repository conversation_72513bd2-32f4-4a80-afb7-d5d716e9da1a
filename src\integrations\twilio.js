const twilio = require('twilio');
const logger = require('../utils/logger');
const config = require('../config/environment');

class TwilioService {
  constructor() {
    this.client = null;
    this.isInitialized = false;
    this.fromNumber = config.twilio.fromNumber;
    
    this.initialize();
  }

  /**
   * Initialize Twilio service
   */
  initialize() {
    try {
      if (!config.twilio.accountSid || !config.twilio.authToken) {
        logger.warn('Twilio credentials not configured. SMS notifications will be disabled.');
        return;
      }

      this.client = twilio(config.twilio.accountSid, config.twilio.authToken);
      this.isInitialized = true;
      
      logger.info('Twilio service initialized successfully');
    } catch (error) {
      logger.error('Failed to initialize Twilio service:', error);
    }
  }

  /**
   * Send SMS message
   */
  async sendSMS({ to, body, mediaUrl = null }) {
    if (!this.isInitialized) {
      logger.warn('Twilio not initialized. SMS not sent.');
      return { success: false, error: 'Twilio not initialized' };
    }

    if (!this.fromNumber) {
      logger.warn('Twilio from number not configured. SMS not sent.');
      return { success: false, error: 'From number not configured' };
    }

    try {
      // Format phone number
      const formattedTo = this.formatPhoneNumber(to);
      
      if (!this.validatePhoneNumber(formattedTo)) {
        throw new Error('Invalid phone number format');
      }

      const messageOptions = {
        body,
        from: this.fromNumber,
        to: formattedTo
      };

      if (mediaUrl) {
        messageOptions.mediaUrl = Array.isArray(mediaUrl) ? mediaUrl : [mediaUrl];
      }

      const message = await this.client.messages.create(messageOptions);
      
      logger.info('SMS sent successfully', {
        to: formattedTo,
        sid: message.sid,
        status: message.status
      });

      return {
        success: true,
        sid: message.sid,
        status: message.status,
        dateCreated: message.dateCreated,
        dateSent: message.dateSent
      };

    } catch (error) {
      logger.error('Failed to send SMS:', {
        to,
        error: error.message,
        code: error.code
      });

      return {
        success: false,
        error: error.message,
        code: error.code
      };
    }
  }

  /**
   * Send bulk SMS messages
   */
  async sendBulkSMS(messages) {
    if (!this.isInitialized) {
      logger.warn('Twilio not initialized. Bulk SMS not sent.');
      return { success: false, error: 'Twilio not initialized' };
    }

    const results = [];

    for (const message of messages) {
      try {
        const result = await this.sendSMS(message);
        results.push({
          to: message.to,
          ...result
        });
      } catch (error) {
        results.push({
          to: message.to,
          success: false,
          error: error.message
        });
      }
    }

    const successCount = results.filter(r => r.success).length;
    const failureCount = results.filter(r => !r.success).length;

    logger.info('Bulk SMS completed', {
      total: messages.length,
      successful: successCount,
      failed: failureCount
    });

    return {
      success: true,
      results,
      summary: {
        total: messages.length,
        successful: successCount,
        failed: failureCount
      }
    };
  }

  /**
   * Send MMS message with media
   */
  async sendMMS({ to, body, mediaUrl }) {
    return await this.sendSMS({ to, body, mediaUrl });
  }

  /**
   * Get message status
   */
  async getMessageStatus(messageSid) {
    if (!this.isInitialized) {
      return { success: false, error: 'Twilio not initialized' };
    }

    try {
      const message = await this.client.messages(messageSid).fetch();
      
      return {
        success: true,
        sid: message.sid,
        status: message.status,
        direction: message.direction,
        from: message.from,
        to: message.to,
        body: message.body,
        dateCreated: message.dateCreated,
        dateSent: message.dateSent,
        dateUpdated: message.dateUpdated,
        errorCode: message.errorCode,
        errorMessage: message.errorMessage
      };

    } catch (error) {
      logger.error('Failed to get message status:', {
        messageSid,
        error: error.message
      });

      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Get account information
   */
  async getAccountInfo() {
    if (!this.isInitialized) {
      return { success: false, error: 'Twilio not initialized' };
    }

    try {
      const account = await this.client.api.accounts(config.twilio.accountSid).fetch();
      
      return {
        success: true,
        accountSid: account.sid,
        friendlyName: account.friendlyName,
        status: account.status,
        type: account.type,
        dateCreated: account.dateCreated,
        dateUpdated: account.dateUpdated
      };

    } catch (error) {
      logger.error('Failed to get account info:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Validate phone number using Twilio Lookup API
   */
  async validatePhoneNumberWithLookup(phoneNumber) {
    if (!this.isInitialized) {
      return { success: false, error: 'Twilio not initialized' };
    }

    try {
      const formattedNumber = this.formatPhoneNumber(phoneNumber);
      
      const phoneNumberInfo = await this.client.lookups.v1
        .phoneNumbers(formattedNumber)
        .fetch({ type: ['carrier'] });

      return {
        success: true,
        phoneNumber: phoneNumberInfo.phoneNumber,
        nationalFormat: phoneNumberInfo.nationalFormat,
        countryCode: phoneNumberInfo.countryCode,
        carrier: phoneNumberInfo.carrier
      };

    } catch (error) {
      logger.error('Phone number validation failed:', {
        phoneNumber,
        error: error.message
      });

      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Format phone number to E.164 format
   */
  formatPhoneNumber(phoneNumber) {
    // Remove all non-digit characters
    const digits = phoneNumber.replace(/\D/g, '');
    
    // If it starts with 1 and has 11 digits, it's already formatted for US
    if (digits.length === 11 && digits.startsWith('1')) {
      return `+${digits}`;
    }
    
    // If it has 10 digits, assume US number and add country code
    if (digits.length === 10) {
      return `+1${digits}`;
    }
    
    // If it already starts with +, return as is
    if (phoneNumber.startsWith('+')) {
      return phoneNumber;
    }
    
    // Otherwise, assume it needs + prefix
    return `+${digits}`;
  }

  /**
   * Validate phone number format
   */
  validatePhoneNumber(phoneNumber) {
    // Basic E.164 format validation
    const e164Regex = /^\+[1-9]\d{1,14}$/;
    return e164Regex.test(phoneNumber);
  }

  /**
   * Create SMS templates for common notifications
   */
  createOrderConfirmationSMS(orderData) {
    return `Hi ${orderData.customerName}! Your order ${orderData.orderNumber} for $${orderData.totalAmount} has been confirmed. Thank you for your purchase!`;
  }

  createShippingNotificationSMS(orderData) {
    return `Hi ${orderData.customerName}! Your order ${orderData.orderNumber} has shipped. Tracking: ${orderData.trackingNumber}. Track at: ${orderData.trackingUrl}`;
  }

  createDeliveryConfirmationSMS(orderData) {
    return `Hi ${orderData.customerName}! Your order ${orderData.orderNumber} has been delivered. Thank you for choosing us!`;
  }

  createOrderCancellationSMS(orderData) {
    return `Hi ${orderData.customerName}, your order ${orderData.orderNumber} has been cancelled. Refund will be processed within 3-5 business days.`;
  }

  createReturnApprovalSMS(returnData) {
    return `Hi ${returnData.customerName}! Your return request ${returnData.returnNumber} has been approved. Please check your email for return instructions.`;
  }

  createLowStockAlertSMS(productData) {
    return `ALERT: Low stock for ${productData.productName} (${productData.sku}) at ${productData.fulfillmentCenter}. Current: ${productData.currentStock}, Reorder point: ${productData.reorderPoint}`;
  }

  /**
   * Send notification based on template
   */
  async sendNotificationSMS(to, template, data) {
    let body;

    switch (template) {
      case 'order_confirmation':
        body = this.createOrderConfirmationSMS(data);
        break;
      case 'order_shipped':
        body = this.createShippingNotificationSMS(data);
        break;
      case 'order_delivered':
        body = this.createDeliveryConfirmationSMS(data);
        break;
      case 'order_cancelled':
        body = this.createOrderCancellationSMS(data);
        break;
      case 'return_approved':
        body = this.createReturnApprovalSMS(data);
        break;
      case 'low_stock_alert':
        body = this.createLowStockAlertSMS(data);
        break;
      default:
        body = data.message || 'Notification from OrderFlow Pro';
    }

    return await this.sendSMS({ to, body });
  }

  /**
   * Schedule SMS (using a simple delay - in production, use a proper job queue)
   */
  async scheduleSMS({ to, body, delayMinutes = 0 }) {
    if (delayMinutes === 0) {
      return await this.sendSMS({ to, body });
    }

    return new Promise((resolve) => {
      setTimeout(async () => {
        const result = await this.sendSMS({ to, body });
        resolve(result);
      }, delayMinutes * 60 * 1000);
    });
  }

  /**
   * Get SMS delivery report
   */
  async getDeliveryReport(messageSids) {
    if (!this.isInitialized) {
      return { success: false, error: 'Twilio not initialized' };
    }

    const reports = [];

    for (const sid of messageSids) {
      try {
        const status = await this.getMessageStatus(sid);
        reports.push(status);
      } catch (error) {
        reports.push({
          sid,
          success: false,
          error: error.message
        });
      }
    }

    const summary = {
      total: messageSids.length,
      delivered: reports.filter(r => r.status === 'delivered').length,
      failed: reports.filter(r => r.status === 'failed').length,
      pending: reports.filter(r => ['queued', 'sending', 'sent'].includes(r.status)).length
    };

    return {
      success: true,
      reports,
      summary
    };
  }

  /**
   * Get service status and configuration
   */
  getStatus() {
    return {
      initialized: this.isInitialized,
      fromNumber: this.fromNumber,
      hasCredentials: !!(config.twilio.accountSid && config.twilio.authToken),
      accountSid: config.twilio.accountSid ? 
        config.twilio.accountSid.substring(0, 8) + '...' : null
    };
  }

  /**
   * Test SMS functionality
   */
  async testSMS(testPhoneNumber) {
    if (!this.isInitialized) {
      return { success: false, error: 'Twilio not initialized' };
    }

    const testMessage = 'This is a test message from OrderFlow Pro SMS service.';
    
    try {
      const result = await this.sendSMS({
        to: testPhoneNumber,
        body: testMessage
      });

      logger.info('SMS test completed', {
        to: testPhoneNumber,
        success: result.success,
        sid: result.sid
      });

      return result;

    } catch (error) {
      logger.error('SMS test failed:', {
        to: testPhoneNumber,
        error: error.message
      });

      return {
        success: false,
        error: error.message
      };
    }
  }
}

module.exports = new TwilioService();
