const express = require('express');
const { body, param, query } = require('express-validator');
const orderController = require('../controllers/orderController');
const { asyncHandler } = require('../middleware/errorHandler');
const { validateRequest } = require('../middleware/validation');
const { authenticate, authorize } = require('../middleware/auth');

const router = express.Router();

// Validation schemas
const createOrderValidation = [
  body('customer_id').isUUID().withMessage('Valid customer ID is required'),
  body('channel_id').isUUID().withMessage('Valid channel ID is required'),
  body('items').isArray({ min: 1 }).withMessage('At least one item is required'),
  body('items.*.product_id').isUUID().withMessage('Valid product ID is required'),
  body('items.*.quantity').isInt({ min: 1 }).withMessage('Quantity must be at least 1'),
  body('items.*.unit_price').isFloat({ min: 0 }).withMessage('Unit price must be non-negative'),
  body('billing_address').isObject().withMessage('Billing address is required'),
  body('billing_address.street_address').notEmpty().withMessage('Street address is required'),
  body('billing_address.city').notEmpty().withMessage('City is required'),
  body('billing_address.state').notEmpty().withMessage('State is required'),
  body('billing_address.postal_code').notEmpty().withMessage('Postal code is required'),
  body('billing_address.country').notEmpty().withMessage('Country is required'),
  body('shipping_address').isObject().withMessage('Shipping address is required'),
  body('shipping_address.street_address').notEmpty().withMessage('Street address is required'),
  body('shipping_address.city').notEmpty().withMessage('City is required'),
  body('shipping_address.state').notEmpty().withMessage('State is required'),
  body('shipping_address.postal_code').notEmpty().withMessage('Postal code is required'),
  body('shipping_address.country').notEmpty().withMessage('Country is required'),
  body('shipping_method').optional().isString(),
  body('notes').optional().isString()
];

const updateOrderValidation = [
  param('id').isUUID().withMessage('Valid order ID is required'),
  body('status').optional().isIn(['pending', 'confirmed', 'processing', 'shipped', 'delivered', 'cancelled', 'returned']),
  body('payment_status').optional().isIn(['pending', 'authorized', 'captured', 'failed', 'refunded']),
  body('shipping_method').optional().isString(),
  body('tracking_number').optional().isString(),
  body('notes').optional().isString()
];

const orderQueryValidation = [
  query('page').optional().isInt({ min: 1 }).withMessage('Page must be a positive integer'),
  query('limit').optional().isInt({ min: 1, max: 100 }).withMessage('Limit must be between 1 and 100'),
  query('status').optional().isIn(['pending', 'confirmed', 'processing', 'shipped', 'delivered', 'cancelled', 'returned']),
  query('payment_status').optional().isIn(['pending', 'authorized', 'captured', 'failed', 'refunded']),
  query('channel_id').optional().isUUID(),
  query('customer_id').optional().isUUID(),
  query('date_from').optional().isISO8601(),
  query('date_to').optional().isISO8601(),
  query('search').optional().isString()
];

// Routes

/**
 * @route   GET /api/orders
 * @desc    Get all orders with filtering and pagination
 * @access  Private
 */
router.get('/', 
  authenticate,
  authorize(['admin', 'manager', 'operator']),
  orderQueryValidation,
  validateRequest,
  asyncHandler(orderController.getOrders)
);

/**
 * @route   GET /api/orders/:id
 * @desc    Get order by ID
 * @access  Private
 */
router.get('/:id',
  authenticate,
  authorize(['admin', 'manager', 'operator']),
  param('id').isUUID().withMessage('Valid order ID is required'),
  validateRequest,
  asyncHandler(orderController.getOrderById)
);

/**
 * @route   POST /api/orders
 * @desc    Create new order
 * @access  Public (for API integrations)
 */
router.post('/',
  createOrderValidation,
  validateRequest,
  asyncHandler(orderController.createOrder)
);

/**
 * @route   PUT /api/orders/:id
 * @desc    Update order
 * @access  Private
 */
router.put('/:id',
  authenticate,
  authorize(['admin', 'manager', 'operator']),
  updateOrderValidation,
  validateRequest,
  asyncHandler(orderController.updateOrder)
);

/**
 * @route   DELETE /api/orders/:id
 * @desc    Cancel order
 * @access  Private
 */
router.delete('/:id',
  authenticate,
  authorize(['admin', 'manager']),
  param('id').isUUID().withMessage('Valid order ID is required'),
  validateRequest,
  asyncHandler(orderController.cancelOrder)
);

/**
 * @route   POST /api/orders/:id/process
 * @desc    Process order (move to next stage)
 * @access  Private
 */
router.post('/:id/process',
  authenticate,
  authorize(['admin', 'manager', 'operator']),
  param('id').isUUID().withMessage('Valid order ID is required'),
  validateRequest,
  asyncHandler(orderController.processOrder)
);

/**
 * @route   POST /api/orders/:id/ship
 * @desc    Mark order as shipped
 * @access  Private
 */
router.post('/:id/ship',
  authenticate,
  authorize(['admin', 'manager', 'operator']),
  param('id').isUUID().withMessage('Valid order ID is required'),
  body('tracking_number').notEmpty().withMessage('Tracking number is required'),
  body('shipping_method').notEmpty().withMessage('Shipping method is required'),
  validateRequest,
  asyncHandler(orderController.shipOrder)
);

/**
 * @route   POST /api/orders/:id/deliver
 * @desc    Mark order as delivered
 * @access  Private
 */
router.post('/:id/deliver',
  authenticate,
  authorize(['admin', 'manager', 'operator']),
  param('id').isUUID().withMessage('Valid order ID is required'),
  validateRequest,
  asyncHandler(orderController.deliverOrder)
);

/**
 * @route   GET /api/orders/:id/tracking
 * @desc    Get order tracking information
 * @access  Public (with order number verification)
 */
router.get('/:id/tracking',
  param('id').isUUID().withMessage('Valid order ID is required'),
  validateRequest,
  asyncHandler(orderController.getOrderTracking)
);

/**
 * @route   GET /api/orders/customer/:customerId
 * @desc    Get orders for specific customer
 * @access  Private
 */
router.get('/customer/:customerId',
  authenticate,
  authorize(['admin', 'manager', 'operator']),
  param('customerId').isUUID().withMessage('Valid customer ID is required'),
  orderQueryValidation,
  validateRequest,
  asyncHandler(orderController.getOrdersByCustomer)
);

/**
 * @route   GET /api/orders/channel/:channelId
 * @desc    Get orders for specific channel
 * @access  Private
 */
router.get('/channel/:channelId',
  authenticate,
  authorize(['admin', 'manager', 'operator']),
  param('channelId').isUUID().withMessage('Valid channel ID is required'),
  orderQueryValidation,
  validateRequest,
  asyncHandler(orderController.getOrdersByChannel)
);

/**
 * @route   POST /api/orders/bulk-update
 * @desc    Bulk update orders
 * @access  Private
 */
router.post('/bulk-update',
  authenticate,
  authorize(['admin', 'manager']),
  body('order_ids').isArray({ min: 1 }).withMessage('At least one order ID is required'),
  body('order_ids.*').isUUID().withMessage('Valid order IDs are required'),
  body('updates').isObject().withMessage('Updates object is required'),
  validateRequest,
  asyncHandler(orderController.bulkUpdateOrders)
);

/**
 * @route   GET /api/orders/export
 * @desc    Export orders to CSV
 * @access  Private
 */
router.get('/export',
  authenticate,
  authorize(['admin', 'manager']),
  orderQueryValidation,
  validateRequest,
  asyncHandler(orderController.exportOrders)
);

/**
 * @route   POST /api/orders/webhook/:channelId
 * @desc    Webhook endpoint for external channels
 * @access  Public (with API key validation)
 */
router.post('/webhook/:channelId',
  param('channelId').isUUID().withMessage('Valid channel ID is required'),
  validateRequest,
  asyncHandler(orderController.handleWebhook)
);

module.exports = router;
