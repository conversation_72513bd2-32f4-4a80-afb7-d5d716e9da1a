const { query } = require('../config/database');
const { cacheAnalytics, getCachedAnalytics } = require('../config/redis');
const logger = require('../utils/logger');

class AnalyticsService {
  /**
   * Get sales metrics for dashboard
   */
  async getSalesMetrics(dateFrom, dateTo, granularity = 'daily') {
    const cacheKey = `sales_metrics_${granularity}_${dateFrom}_${dateTo}`;
    
    // Try cache first
    const cached = await getCachedAnalytics('sales_metrics', cacheKey);
    if (cached) {
      return cached;
    }

    let dateFormat, dateInterval;
    
    switch (granularity) {
      case 'hourly':
        dateFormat = 'YYYY-MM-DD HH24:00:00';
        dateInterval = '1 hour';
        break;
      case 'daily':
        dateFormat = 'YYYY-MM-DD';
        dateInterval = '1 day';
        break;
      case 'weekly':
        dateFormat = 'YYYY-"W"WW';
        dateInterval = '1 week';
        break;
      case 'monthly':
        dateFormat = 'YYYY-MM';
        dateInterval = '1 month';
        break;
      default:
        dateFormat = 'YYYY-MM-DD';
        dateInterval = '1 day';
    }

    const metricsQuery = `
      WITH date_series AS (
        SELECT generate_series(
          $1::timestamp,
          $2::timestamp,
          $3::interval
        )::date as date
      ),
      order_metrics AS (
        SELECT 
          TO_CHAR(o.created_at, $4) as period,
          COUNT(o.id) as total_orders,
          SUM(o.total_amount) as total_revenue,
          AVG(o.total_amount) as avg_order_value,
          COUNT(DISTINCT o.customer_id) as unique_customers,
          SUM(CASE WHEN o.status = 'delivered' THEN 1 ELSE 0 END) as delivered_orders,
          SUM(CASE WHEN o.status = 'cancelled' THEN 1 ELSE 0 END) as cancelled_orders
        FROM orders o
        WHERE o.created_at >= $1 AND o.created_at <= $2
        GROUP BY TO_CHAR(o.created_at, $4)
      )
      SELECT 
        ds.date,
        COALESCE(om.total_orders, 0) as total_orders,
        COALESCE(om.total_revenue, 0) as total_revenue,
        COALESCE(om.avg_order_value, 0) as avg_order_value,
        COALESCE(om.unique_customers, 0) as unique_customers,
        COALESCE(om.delivered_orders, 0) as delivered_orders,
        COALESCE(om.cancelled_orders, 0) as cancelled_orders,
        CASE 
          WHEN COALESCE(om.total_orders, 0) > 0 
          THEN (COALESCE(om.delivered_orders, 0)::float / om.total_orders * 100)
          ELSE 0 
        END as delivery_rate
      FROM date_series ds
      LEFT JOIN order_metrics om ON TO_CHAR(ds.date, $4) = om.period
      ORDER BY ds.date
    `;

    const result = await query(metricsQuery, [dateFrom, dateTo, dateInterval, dateFormat]);
    
    const metrics = {
      period: { from: dateFrom, to: dateTo, granularity },
      data: result.rows,
      summary: this.calculateSummaryMetrics(result.rows)
    };

    // Cache for 1 hour
    await cacheAnalytics('sales_metrics', cacheKey, metrics, 3600);

    return metrics;
  }

  /**
   * Get inventory analytics
   */
  async getInventoryAnalytics() {
    const cacheKey = 'inventory_analytics';
    
    // Try cache first
    const cached = await getCachedAnalytics('inventory', cacheKey);
    if (cached) {
      return cached;
    }

    const inventoryQuery = `
      SELECT 
        COUNT(DISTINCT i.product_id) as total_products,
        COUNT(DISTINCT i.fulfillment_center_id) as total_fulfillment_centers,
        SUM(i.quantity_available) as total_available_quantity,
        SUM(i.quantity_reserved) as total_reserved_quantity,
        COUNT(CASE WHEN i.quantity_available <= i.reorder_point THEN 1 END) as low_stock_items,
        COUNT(CASE WHEN i.quantity_available = 0 THEN 1 END) as out_of_stock_items,
        AVG(i.quantity_available) as avg_stock_level
      FROM inventory i
      JOIN products p ON i.product_id = p.id
      JOIN fulfillment_centers fc ON i.fulfillment_center_id = fc.id
      WHERE p.is_active = true AND fc.is_active = true
    `;

    const topProductsQuery = `
      SELECT 
        p.id,
        p.name,
        p.sku,
        SUM(oi.quantity) as total_sold,
        SUM(oi.total_price) as total_revenue
      FROM products p
      JOIN order_items oi ON p.id = oi.product_id
      JOIN orders o ON oi.order_id = o.id
      WHERE o.created_at >= NOW() - INTERVAL '30 days'
        AND o.status IN ('delivered', 'shipped')
      GROUP BY p.id, p.name, p.sku
      ORDER BY total_sold DESC
      LIMIT 10
    `;

    const lowStockQuery = `
      SELECT 
        p.name as product_name,
        p.sku,
        fc.name as fulfillment_center_name,
        i.quantity_available,
        i.reorder_point,
        i.max_stock_level
      FROM inventory i
      JOIN products p ON i.product_id = p.id
      JOIN fulfillment_centers fc ON i.fulfillment_center_id = fc.id
      WHERE i.quantity_available <= i.reorder_point
        AND p.is_active = true
        AND fc.is_active = true
      ORDER BY i.quantity_available ASC
      LIMIT 20
    `;

    const [inventoryResult, topProductsResult, lowStockResult] = await Promise.all([
      query(inventoryQuery),
      query(topProductsQuery),
      query(lowStockQuery)
    ]);

    const analytics = {
      summary: inventoryResult.rows[0],
      top_products: topProductsResult.rows,
      low_stock_items: lowStockResult.rows,
      generated_at: new Date()
    };

    // Cache for 30 minutes
    await cacheAnalytics('inventory', cacheKey, analytics, 1800);

    return analytics;
  }

  /**
   * Get customer analytics
   */
  async getCustomerAnalytics(dateFrom, dateTo) {
    const cacheKey = `customer_analytics_${dateFrom}_${dateTo}`;
    
    const cached = await getCachedAnalytics('customer', cacheKey);
    if (cached) {
      return cached;
    }

    const customerMetricsQuery = `
      SELECT 
        COUNT(DISTINCT c.id) as total_customers,
        COUNT(DISTINCT CASE WHEN o.created_at >= $1 THEN c.id END) as new_customers,
        COUNT(DISTINCT CASE WHEN repeat_customers.customer_id IS NOT NULL THEN c.id END) as repeat_customers,
        AVG(customer_stats.total_orders) as avg_orders_per_customer,
        AVG(customer_stats.total_spent) as avg_customer_value
      FROM customers c
      LEFT JOIN orders o ON c.id = o.customer_id
      LEFT JOIN (
        SELECT customer_id
        FROM orders
        WHERE created_at BETWEEN $1 AND $2
        GROUP BY customer_id
        HAVING COUNT(*) > 1
      ) repeat_customers ON c.id = repeat_customers.customer_id
      LEFT JOIN (
        SELECT 
          customer_id,
          COUNT(*) as total_orders,
          SUM(total_amount) as total_spent
        FROM orders
        WHERE created_at BETWEEN $1 AND $2
        GROUP BY customer_id
      ) customer_stats ON c.id = customer_stats.customer_id
    `;

    const topCustomersQuery = `
      SELECT 
        c.id,
        c.email,
        c.first_name,
        c.last_name,
        COUNT(o.id) as total_orders,
        SUM(o.total_amount) as total_spent,
        AVG(o.total_amount) as avg_order_value,
        MAX(o.created_at) as last_order_date
      FROM customers c
      JOIN orders o ON c.id = o.customer_id
      WHERE o.created_at BETWEEN $1 AND $2
      GROUP BY c.id, c.email, c.first_name, c.last_name
      ORDER BY total_spent DESC
      LIMIT 10
    `;

    const customerSegmentationQuery = `
      SELECT 
        CASE 
          WHEN customer_stats.total_spent >= 1000 THEN 'VIP'
          WHEN customer_stats.total_spent >= 500 THEN 'High Value'
          WHEN customer_stats.total_spent >= 100 THEN 'Regular'
          ELSE 'New/Low Value'
        END as segment,
        COUNT(*) as customer_count,
        AVG(customer_stats.total_spent) as avg_spent,
        SUM(customer_stats.total_spent) as total_revenue
      FROM (
        SELECT 
          customer_id,
          SUM(total_amount) as total_spent
        FROM orders
        WHERE created_at BETWEEN $1 AND $2
        GROUP BY customer_id
      ) customer_stats
      GROUP BY segment
      ORDER BY avg_spent DESC
    `;

    const [metricsResult, topCustomersResult, segmentationResult] = await Promise.all([
      query(customerMetricsQuery, [dateFrom, dateTo]),
      query(topCustomersQuery, [dateFrom, dateTo]),
      query(customerSegmentationQuery, [dateFrom, dateTo])
    ]);

    const analytics = {
      period: { from: dateFrom, to: dateTo },
      summary: metricsResult.rows[0],
      top_customers: topCustomersResult.rows,
      segmentation: segmentationResult.rows,
      generated_at: new Date()
    };

    // Cache for 1 hour
    await cacheAnalytics('customer', cacheKey, analytics, 3600);

    return analytics;
  }

  /**
   * Get fulfillment center performance
   */
  async getFulfillmentCenterPerformance(dateFrom, dateTo) {
    const cacheKey = `fc_performance_${dateFrom}_${dateTo}`;
    
    const cached = await getCachedAnalytics('fulfillment', cacheKey);
    if (cached) {
      return cached;
    }

    const performanceQuery = `
      SELECT 
        fc.id,
        fc.name,
        fc.code,
        COUNT(o.id) as total_orders,
        SUM(o.total_amount) as total_revenue,
        AVG(o.total_amount) as avg_order_value,
        COUNT(CASE WHEN o.status = 'delivered' THEN 1 END) as delivered_orders,
        COUNT(CASE WHEN o.status = 'cancelled' THEN 1 END) as cancelled_orders,
        AVG(EXTRACT(EPOCH FROM (o.updated_at - o.created_at))/3600) as avg_processing_hours,
        SUM(i.quantity_available) as total_inventory,
        COUNT(CASE WHEN i.quantity_available <= i.reorder_point THEN 1 END) as low_stock_items
      FROM fulfillment_centers fc
      LEFT JOIN orders o ON fc.id = o.fulfillment_center_id 
        AND o.created_at BETWEEN $1 AND $2
      LEFT JOIN inventory i ON fc.id = i.fulfillment_center_id
      WHERE fc.is_active = true
      GROUP BY fc.id, fc.name, fc.code
      ORDER BY total_orders DESC
    `;

    const result = await query(performanceQuery, [dateFrom, dateTo]);
    
    const performance = result.rows.map(fc => ({
      ...fc,
      delivery_rate: fc.total_orders > 0 ? (fc.delivered_orders / fc.total_orders * 100) : 0,
      cancellation_rate: fc.total_orders > 0 ? (fc.cancelled_orders / fc.total_orders * 100) : 0
    }));

    const analytics = {
      period: { from: dateFrom, to: dateTo },
      fulfillment_centers: performance,
      generated_at: new Date()
    };

    // Cache for 1 hour
    await cacheAnalytics('fulfillment', cacheKey, analytics, 3600);

    return analytics;
  }

  /**
   * Get channel performance analytics
   */
  async getChannelPerformance(dateFrom, dateTo) {
    const cacheKey = `channel_performance_${dateFrom}_${dateTo}`;
    
    const cached = await getCachedAnalytics('channel', cacheKey);
    if (cached) {
      return cached;
    }

    const channelQuery = `
      SELECT 
        sc.id,
        sc.name,
        sc.type,
        COUNT(o.id) as total_orders,
        SUM(o.total_amount) as total_revenue,
        AVG(o.total_amount) as avg_order_value,
        COUNT(DISTINCT o.customer_id) as unique_customers,
        COUNT(CASE WHEN o.status = 'delivered' THEN 1 END) as delivered_orders,
        COUNT(CASE WHEN o.status = 'cancelled' THEN 1 END) as cancelled_orders
      FROM sales_channels sc
      LEFT JOIN orders o ON sc.id = o.channel_id 
        AND o.created_at BETWEEN $1 AND $2
      WHERE sc.is_active = true
      GROUP BY sc.id, sc.name, sc.type
      ORDER BY total_revenue DESC
    `;

    const result = await query(channelQuery, [dateFrom, dateTo]);
    
    const totalRevenue = result.rows.reduce((sum, channel) => sum + parseFloat(channel.total_revenue || 0), 0);
    
    const performance = result.rows.map(channel => ({
      ...channel,
      revenue_share: totalRevenue > 0 ? (parseFloat(channel.total_revenue || 0) / totalRevenue * 100) : 0,
      delivery_rate: channel.total_orders > 0 ? (channel.delivered_orders / channel.total_orders * 100) : 0,
      cancellation_rate: channel.total_orders > 0 ? (channel.cancelled_orders / channel.total_orders * 100) : 0
    }));

    const analytics = {
      period: { from: dateFrom, to: dateTo },
      channels: performance,
      total_revenue: totalRevenue,
      generated_at: new Date()
    };

    // Cache for 1 hour
    await cacheAnalytics('channel', cacheKey, analytics, 3600);

    return analytics;
  }

  /**
   * Get return analytics
   */
  async getReturnAnalytics(dateFrom, dateTo) {
    const cacheKey = `return_analytics_${dateFrom}_${dateTo}`;
    
    const cached = await getCachedAnalytics('returns', cacheKey);
    if (cached) {
      return cached;
    }

    const returnMetricsQuery = `
      SELECT 
        COUNT(r.id) as total_returns,
        COUNT(CASE WHEN r.status = 'approved' THEN 1 END) as approved_returns,
        COUNT(CASE WHEN r.status = 'rejected' THEN 1 END) as rejected_returns,
        COUNT(CASE WHEN r.status = 'processed' THEN 1 END) as processed_returns,
        SUM(r.refund_amount) as total_refund_amount,
        AVG(r.refund_amount) as avg_refund_amount,
        AVG(EXTRACT(EPOCH FROM (r.updated_at - r.created_at))/3600) as avg_processing_hours
      FROM returns r
      WHERE r.created_at BETWEEN $1 AND $2
    `;

    const returnReasonsQuery = `
      SELECT 
        r.reason,
        COUNT(*) as count,
        AVG(r.refund_amount) as avg_refund_amount,
        COUNT(*) * 100.0 / SUM(COUNT(*)) OVER() as percentage
      FROM returns r
      WHERE r.created_at BETWEEN $1 AND $2
      GROUP BY r.reason
      ORDER BY count DESC
    `;

    const returnRateQuery = `
      SELECT 
        COUNT(DISTINCT r.order_id) * 100.0 / COUNT(DISTINCT o.id) as return_rate
      FROM orders o
      LEFT JOIN returns r ON o.id = r.order_id
      WHERE o.created_at BETWEEN $1 AND $2
        AND o.status = 'delivered'
    `;

    const [metricsResult, reasonsResult, rateResult] = await Promise.all([
      query(returnMetricsQuery, [dateFrom, dateTo]),
      query(returnReasonsQuery, [dateFrom, dateTo]),
      query(returnRateQuery, [dateFrom, dateTo])
    ]);

    const analytics = {
      period: { from: dateFrom, to: dateTo },
      summary: {
        ...metricsResult.rows[0],
        return_rate: parseFloat(rateResult.rows[0]?.return_rate || 0)
      },
      reasons: reasonsResult.rows,
      generated_at: new Date()
    };

    // Cache for 1 hour
    await cacheAnalytics('returns', cacheKey, analytics, 3600);

    return analytics;
  }

  /**
   * Get comprehensive dashboard data
   */
  async getDashboardData(dateFrom, dateTo) {
    try {
      const [
        salesMetrics,
        inventoryAnalytics,
        customerAnalytics,
        channelPerformance,
        returnAnalytics
      ] = await Promise.all([
        this.getSalesMetrics(dateFrom, dateTo),
        this.getInventoryAnalytics(),
        this.getCustomerAnalytics(dateFrom, dateTo),
        this.getChannelPerformance(dateFrom, dateTo),
        this.getReturnAnalytics(dateFrom, dateTo)
      ]);

      return {
        period: { from: dateFrom, to: dateTo },
        sales: salesMetrics,
        inventory: inventoryAnalytics,
        customers: customerAnalytics,
        channels: channelPerformance,
        returns: returnAnalytics,
        generated_at: new Date()
      };

    } catch (error) {
      logger.error('Error generating dashboard data:', error);
      throw error;
    }
  }

  /**
   * Get real-time metrics
   */
  async getRealTimeMetrics() {
    const metricsQuery = `
      SELECT 
        (SELECT COUNT(*) FROM orders WHERE created_at >= CURRENT_DATE) as orders_today,
        (SELECT COALESCE(SUM(total_amount), 0) FROM orders WHERE created_at >= CURRENT_DATE) as revenue_today,
        (SELECT COUNT(*) FROM orders WHERE status = 'pending') as pending_orders,
        (SELECT COUNT(*) FROM inventory WHERE quantity_available <= reorder_point) as low_stock_alerts,
        (SELECT COUNT(*) FROM returns WHERE status = 'requested') as pending_returns,
        (SELECT COUNT(*) FROM purchase_orders WHERE status = 'pending') as pending_purchase_orders
    `;

    const result = await query(metricsQuery);
    return result.rows[0];
  }

  /**
   * Calculate summary metrics from time series data
   */
  calculateSummaryMetrics(data) {
    if (!data || data.length === 0) {
      return {
        total_orders: 0,
        total_revenue: 0,
        avg_order_value: 0,
        total_customers: 0,
        delivery_rate: 0
      };
    }

    const totals = data.reduce((acc, row) => ({
      total_orders: acc.total_orders + parseInt(row.total_orders || 0),
      total_revenue: acc.total_revenue + parseFloat(row.total_revenue || 0),
      unique_customers: acc.unique_customers + parseInt(row.unique_customers || 0),
      delivered_orders: acc.delivered_orders + parseInt(row.delivered_orders || 0)
    }), {
      total_orders: 0,
      total_revenue: 0,
      unique_customers: 0,
      delivered_orders: 0
    });

    return {
      total_orders: totals.total_orders,
      total_revenue: totals.total_revenue,
      avg_order_value: totals.total_orders > 0 ? totals.total_revenue / totals.total_orders : 0,
      total_customers: totals.unique_customers,
      delivery_rate: totals.total_orders > 0 ? (totals.delivered_orders / totals.total_orders * 100) : 0
    };
  }

  /**
   * Export analytics data to CSV
   */
  async exportAnalyticsToCSV(type, dateFrom, dateTo) {
    let data;
    let headers;

    switch (type) {
      case 'sales':
        data = await this.getSalesMetrics(dateFrom, dateTo);
        headers = ['Date', 'Orders', 'Revenue', 'Avg Order Value', 'Customers', 'Delivery Rate'];
        break;
      case 'customers':
        data = await this.getCustomerAnalytics(dateFrom, dateTo);
        headers = ['Email', 'Name', 'Orders', 'Total Spent', 'Avg Order Value', 'Last Order'];
        break;
      case 'channels':
        data = await this.getChannelPerformance(dateFrom, dateTo);
        headers = ['Channel', 'Type', 'Orders', 'Revenue', 'Revenue Share', 'Delivery Rate'];
        break;
      default:
        throw new Error(`Unsupported export type: ${type}`);
    }

    // Convert to CSV format
    const csvRows = [headers.join(',')];
    
    if (type === 'sales') {
      data.data.forEach(row => {
        csvRows.push([
          row.date,
          row.total_orders,
          row.total_revenue,
          row.avg_order_value,
          row.unique_customers,
          row.delivery_rate
        ].join(','));
      });
    } else if (type === 'customers') {
      data.top_customers.forEach(customer => {
        csvRows.push([
          customer.email,
          `${customer.first_name} ${customer.last_name}`,
          customer.total_orders,
          customer.total_spent,
          customer.avg_order_value,
          customer.last_order_date
        ].join(','));
      });
    } else if (type === 'channels') {
      data.channels.forEach(channel => {
        csvRows.push([
          channel.name,
          channel.type,
          channel.total_orders,
          channel.total_revenue,
          channel.revenue_share,
          channel.delivery_rate
        ].join(','));
      });
    }

    return csvRows.join('\n');
  }
}

module.exports = new AnalyticsService();
