const { query } = require('../config/database');
const { ChannelIntegrationFactory } = require('../integrations/channelIntegrations');
const orderService = require('./orderService');
const inventoryService = require('./inventoryService');
const logger = require('../utils/logger');
const cron = require('node-cron');

class ChannelSyncService {
  constructor() {
    this.integrations = new Map();
    this.syncJobs = new Map();
    this.isInitialized = false;
  }

  /**
   * Initialize channel integrations
   */
  async initialize() {
    if (this.isInitialized) return;

    try {
      // Load active channels from database
      const channelsQuery = `
        SELECT id, name, type, api_endpoint, api_key, is_active
        FROM sales_channels 
        WHERE is_active = true
      `;

      const result = await query(channelsQuery);
      
      for (const channel of result.rows) {
        await this.setupChannelIntegration(channel);
      }

      // Start sync jobs
      this.startSyncJobs();
      
      this.isInitialized = true;
      logger.info('Channel sync service initialized', {
        channelCount: this.integrations.size
      });

    } catch (error) {
      logger.error('Failed to initialize channel sync service:', error);
      throw error;
    }
  }

  /**
   * Setup integration for a specific channel
   */
  async setupChannelIntegration(channel) {
    try {
      // Get channel configuration
      const config = await this.getChannelConfig(channel);
      
      // Create integration instance
      const integration = ChannelIntegrationFactory.create(channel.type, config);
      
      this.integrations.set(channel.id, {
        channel,
        integration,
        lastSync: null,
        syncStatus: 'ready'
      });

      logger.info('Channel integration setup completed', {
        channelId: channel.id,
        channelName: channel.name,
        channelType: channel.type
      });

    } catch (error) {
      logger.error('Failed to setup channel integration:', {
        channelId: channel.id,
        error: error.message
      });
    }
  }

  /**
   * Get channel configuration
   */
  async getChannelConfig(channel) {
    // In a real implementation, this would fetch encrypted credentials
    // from a secure configuration store
    const baseConfig = {
      name: channel.name,
      type: channel.type,
      api_endpoint: channel.api_endpoint
    };

    // Channel-specific configuration
    switch (channel.type.toLowerCase()) {
      case 'shopify':
        return {
          ...baseConfig,
          shop_domain: process.env[`SHOPIFY_SHOP_DOMAIN_${channel.id}`],
          access_token: process.env[`SHOPIFY_ACCESS_TOKEN_${channel.id}`],
          location_id: process.env[`SHOPIFY_LOCATION_ID_${channel.id}`]
        };

      case 'amazon':
        return {
          ...baseConfig,
          marketplace_id: process.env[`AMAZON_MARKETPLACE_ID_${channel.id}`],
          access_token: process.env[`AMAZON_ACCESS_TOKEN_${channel.id}`],
          refresh_token: process.env[`AMAZON_REFRESH_TOKEN_${channel.id}`],
          client_id: process.env[`AMAZON_CLIENT_ID_${channel.id}`],
          client_secret: process.env[`AMAZON_CLIENT_SECRET_${channel.id}`]
        };

      case 'ebay':
        return {
          ...baseConfig,
          access_token: process.env[`EBAY_ACCESS_TOKEN_${channel.id}`],
          refresh_token: process.env[`EBAY_REFRESH_TOKEN_${channel.id}`],
          sandbox: process.env.NODE_ENV !== 'production'
        };

      default:
        return baseConfig;
    }
  }

  /**
   * Sync orders from all channels
   */
  async syncAllChannels() {
    logger.info('Starting sync for all channels');

    const syncPromises = Array.from(this.integrations.keys()).map(channelId =>
      this.syncChannel(channelId).catch(error => {
        logger.error(`Sync failed for channel ${channelId}:`, error);
        return { channelId, success: false, error: error.message };
      })
    );

    const results = await Promise.all(syncPromises);
    
    const summary = {
      total: results.length,
      successful: results.filter(r => r.success !== false).length,
      failed: results.filter(r => r.success === false).length
    };

    logger.info('Channel sync completed', summary);
    return { results, summary };
  }

  /**
   * Sync orders from specific channel
   */
  async syncChannel(channelId) {
    const channelData = this.integrations.get(channelId);
    if (!channelData) {
      throw new Error(`Channel ${channelId} not found`);
    }

    const { channel, integration } = channelData;
    
    try {
      // Update sync status
      channelData.syncStatus = 'syncing';
      
      // Get last sync time
      const lastSync = channelData.lastSync || new Date(Date.now() - 24 * 60 * 60 * 1000);
      
      // Fetch orders from channel
      const externalOrders = await integration.fetchOrders({
        created_after: lastSync.toISOString(),
        limit: 100
      });

      logger.info(`Fetched ${externalOrders.length} orders from ${channel.name}`);

      // Process each order
      const processedOrders = [];
      for (const externalOrder of externalOrders) {
        try {
          const processedOrder = await this.processExternalOrder(channelId, externalOrder);
          processedOrders.push(processedOrder);
        } catch (error) {
          logger.error('Failed to process external order:', {
            channelId,
            externalOrderId: externalOrder.external_id,
            error: error.message
          });
        }
      }

      // Update sync status
      channelData.lastSync = new Date();
      channelData.syncStatus = 'completed';

      logger.info('Channel sync completed successfully', {
        channelId,
        channelName: channel.name,
        ordersProcessed: processedOrders.length
      });

      return {
        channelId,
        channelName: channel.name,
        ordersProcessed: processedOrders.length,
        success: true
      };

    } catch (error) {
      channelData.syncStatus = 'failed';
      logger.error('Channel sync failed:', {
        channelId,
        channelName: channel.name,
        error: error.message
      });
      throw error;
    }
  }

  /**
   * Process external order
   */
  async processExternalOrder(channelId, externalOrder) {
    // Check if order already exists
    const existingOrderQuery = `
      SELECT id FROM orders 
      WHERE channel_id = $1 AND (
        order_number = $2 OR 
        notes LIKE '%external_id:' || $3 || '%'
      )
    `;

    const existingResult = await query(existingOrderQuery, [
      channelId,
      externalOrder.external_order_number,
      externalOrder.external_id
    ]);

    if (existingResult.rows.length > 0) {
      // Order exists, update if needed
      return await this.updateExistingOrder(existingResult.rows[0].id, externalOrder);
    } else {
      // Create new order
      return await this.createOrderFromExternal(channelId, externalOrder);
    }
  }

  /**
   * Create order from external data
   */
  async createOrderFromExternal(channelId, externalOrder) {
    // Find or create customer
    const customer = await this.findOrCreateCustomer(externalOrder);
    
    // Map external items to internal products
    const mappedItems = await this.mapExternalItems(externalOrder.items);

    // Create order data
    const orderData = {
      customer_id: customer.id,
      channel_id: channelId,
      items: mappedItems,
      billing_address: externalOrder.billing_address || externalOrder.shipping_address,
      shipping_address: externalOrder.shipping_address,
      shipping_method: externalOrder.shipping_method,
      notes: `External Order ID: ${externalOrder.external_id}\nExternal Order Number: ${externalOrder.external_order_number}`,
      total_amount: externalOrder.total_amount,
      currency: externalOrder.currency
    };

    return await orderService.createOrder(orderData);
  }

  /**
   * Update existing order
   */
  async updateExistingOrder(orderId, externalOrder) {
    const updates = {
      status: externalOrder.status,
      updated_at: new Date()
    };

    return await orderService.updateOrder(orderId, updates);
  }

  /**
   * Find or create customer
   */
  async findOrCreateCustomer(externalOrder) {
    // Try to find existing customer by email
    const customerQuery = `
      SELECT * FROM customers WHERE email = $1
    `;

    const result = await query(customerQuery, [externalOrder.customer_email]);

    if (result.rows.length > 0) {
      return result.rows[0];
    }

    // Create new customer
    const createCustomerQuery = `
      INSERT INTO customers (id, email, first_name, last_name, created_at, updated_at)
      VALUES (uuid_generate_v4(), $1, $2, $3, NOW(), NOW())
      RETURNING *
    `;

    const firstName = externalOrder.shipping_address?.first_name || 'Unknown';
    const lastName = externalOrder.shipping_address?.last_name || 'Customer';

    const createResult = await query(createCustomerQuery, [
      externalOrder.customer_email,
      firstName,
      lastName
    ]);

    return createResult.rows[0];
  }

  /**
   * Map external items to internal products
   */
  async mapExternalItems(externalItems) {
    const mappedItems = [];

    for (const item of externalItems) {
      // Try to find product by SKU
      const productQuery = `
        SELECT id FROM products WHERE sku = $1
      `;

      const result = await query(productQuery, [item.product_sku]);

      if (result.rows.length > 0) {
        mappedItems.push({
          product_id: result.rows[0].id,
          quantity: item.quantity,
          unit_price: item.unit_price
        });
      } else {
        logger.warn('Product not found for external item:', {
          sku: item.product_sku,
          external_id: item.external_id
        });
      }
    }

    return mappedItems;
  }

  /**
   * Sync inventory to channels
   */
  async syncInventoryToChannels() {
    logger.info('Starting inventory sync to channels');

    for (const [channelId, channelData] of this.integrations) {
      try {
        await this.syncInventoryToChannel(channelId);
      } catch (error) {
        logger.error(`Inventory sync failed for channel ${channelId}:`, error);
      }
    }
  }

  /**
   * Sync inventory to specific channel
   */
  async syncInventoryToChannel(channelId) {
    const channelData = this.integrations.get(channelId);
    if (!channelData) return;

    const { channel, integration } = channelData;

    // Get products that need inventory sync
    const productsQuery = `
      SELECT p.id, p.sku, SUM(i.quantity_available) as total_quantity
      FROM products p
      JOIN inventory i ON p.id = i.product_id
      WHERE p.is_active = true
      GROUP BY p.id, p.sku
    `;

    const result = await query(productsQuery);
    
    if (typeof integration.syncInventory === 'function') {
      const syncResults = await integration.syncInventory(result.rows);
      
      logger.info('Inventory sync completed', {
        channelId,
        channelName: channel.name,
        productsUpdated: syncResults.filter(r => r.success).length,
        productsFailed: syncResults.filter(r => !r.success).length
      });
    }
  }

  /**
   * Start scheduled sync jobs
   */
  startSyncJobs() {
    // Sync orders every 15 minutes
    const orderSyncJob = cron.schedule('*/15 * * * *', async () => {
      try {
        await this.syncAllChannels();
      } catch (error) {
        logger.error('Scheduled order sync failed:', error);
      }
    }, { scheduled: false });

    // Sync inventory every hour
    const inventorySyncJob = cron.schedule('0 * * * *', async () => {
      try {
        await this.syncInventoryToChannels();
      } catch (error) {
        logger.error('Scheduled inventory sync failed:', error);
      }
    }, { scheduled: false });

    // Start jobs
    orderSyncJob.start();
    inventorySyncJob.start();

    this.syncJobs.set('orders', orderSyncJob);
    this.syncJobs.set('inventory', inventorySyncJob);

    logger.info('Sync jobs started');
  }

  /**
   * Stop sync jobs
   */
  stopSyncJobs() {
    for (const [name, job] of this.syncJobs) {
      job.destroy();
      logger.info(`Sync job ${name} stopped`);
    }
    this.syncJobs.clear();
  }

  /**
   * Get sync status for all channels
   */
  getSyncStatus() {
    const status = {};
    
    for (const [channelId, channelData] of this.integrations) {
      status[channelId] = {
        channelName: channelData.channel.name,
        syncStatus: channelData.syncStatus,
        lastSync: channelData.lastSync
      };
    }

    return status;
  }

  /**
   * Manual sync trigger
   */
  async triggerSync(channelId = null) {
    if (channelId) {
      return await this.syncChannel(channelId);
    } else {
      return await this.syncAllChannels();
    }
  }
}

module.exports = new ChannelSyncService();
