const { query, transaction } = require('../config/database');
const { cacheInventory, getCachedInventory } = require('../config/redis');
const logger = require('../utils/logger');
const { v4: uuidv4 } = require('uuid');

class InventoryService {
  /**
   * Check inventory availability for a product
   */
  async checkAvailability(productId, requestedQuantity, fulfillmentCenterId = null) {
    try {
      // Try cache first
      if (fulfillmentCenterId) {
        const cached = await getCachedInventory(productId, fulfillmentCenterId);
        if (cached) {
          return {
            available: cached.quantity_available >= requestedQuantity,
            quantity: cached.quantity_available,
            reserved: cached.quantity_reserved,
            fulfillmentCenterId
          };
        }
      }

      let inventoryQuery;
      let queryParams;

      if (fulfillmentCenterId) {
        // Check specific fulfillment center
        inventoryQuery = `
          SELECT 
            i.*,
            fc.name as fulfillment_center_name
          FROM inventory i
          JOIN fulfillment_centers fc ON i.fulfillment_center_id = fc.id
          WHERE i.product_id = $1 AND i.fulfillment_center_id = $2
        `;
        queryParams = [productId, fulfillmentCenterId];
      } else {
        // Check all fulfillment centers
        inventoryQuery = `
          SELECT 
            i.*,
            fc.name as fulfillment_center_name
          FROM inventory i
          JOIN fulfillment_centers fc ON i.fulfillment_center_id = fc.id
          WHERE i.product_id = $1 AND fc.is_active = true
          ORDER BY i.quantity_available DESC
        `;
        queryParams = [productId];
      }

      const result = await query(inventoryQuery, queryParams);

      if (result.rows.length === 0) {
        return {
          available: false,
          quantity: 0,
          reserved: 0,
          fulfillmentCenterId: null,
          message: 'Product not found in inventory'
        };
      }

      if (fulfillmentCenterId) {
        const inventory = result.rows[0];
        
        // Cache the result
        await cacheInventory(productId, fulfillmentCenterId, inventory, 300);

        return {
          available: inventory.quantity_available >= requestedQuantity,
          quantity: inventory.quantity_available,
          reserved: inventory.quantity_reserved,
          fulfillmentCenterId: inventory.fulfillment_center_id
        };
      } else {
        // Find best fulfillment center with sufficient stock
        const totalAvailable = result.rows.reduce((sum, row) => sum + row.quantity_available, 0);
        const bestFC = result.rows.find(row => row.quantity_available >= requestedQuantity);

        return {
          available: totalAvailable >= requestedQuantity,
          quantity: totalAvailable,
          bestFulfillmentCenter: bestFC || result.rows[0],
          allLocations: result.rows
        };
      }

    } catch (error) {
      logger.error('Error checking inventory availability:', error);
      throw error;
    }
  }

  /**
   * Reserve inventory for an order
   */
  async reserveInventory(orderId, items) {
    return await transaction(async (client) => {
      const reservations = [];

      for (const item of items) {
        // Find best fulfillment center for this item
        const availability = await this.checkAvailability(item.product_id, item.quantity);
        
        if (!availability.available) {
          throw new Error(`Insufficient inventory for product ${item.product_id}`);
        }

        const fulfillmentCenterId = availability.bestFulfillmentCenter.fulfillment_center_id;

        // Reserve inventory
        const reserveQuery = `
          UPDATE inventory 
          SET 
            quantity_available = quantity_available - $1,
            quantity_reserved = quantity_reserved + $1,
            last_updated = NOW()
          WHERE product_id = $2 AND fulfillment_center_id = $3
          RETURNING *
        `;

        const reserveResult = await client.query(reserveQuery, [
          item.quantity,
          item.product_id,
          fulfillmentCenterId
        ]);

        if (reserveResult.rows.length === 0) {
          throw new Error(`Failed to reserve inventory for product ${item.product_id}`);
        }

        // Log inventory movement
        await this.logInventoryMovement(
          client,
          item.product_id,
          fulfillmentCenterId,
          'out',
          item.quantity,
          'order',
          orderId,
          'Inventory reserved for order'
        );

        reservations.push({
          product_id: item.product_id,
          fulfillment_center_id: fulfillmentCenterId,
          quantity: item.quantity,
          reserved_at: new Date()
        });

        // Clear cache
        await this.clearInventoryCache(item.product_id, fulfillmentCenterId);
      }

      logger.logInventory(null, null, null, 'inventory_reserved', {
        order_id: orderId,
        reservations
      });

      return reservations;
    });
  }

  /**
   * Release reserved inventory
   */
  async releaseReservedInventory(orderId) {
    return await transaction(async (client) => {
      // Get order items to release
      const orderItemsQuery = `
        SELECT oi.product_id, oi.quantity, o.fulfillment_center_id
        FROM order_items oi
        JOIN orders o ON oi.order_id = o.id
        WHERE o.id = $1
      `;

      const orderItemsResult = await client.query(orderItemsQuery, [orderId]);

      for (const item of orderItemsResult.rows) {
        // Release inventory
        const releaseQuery = `
          UPDATE inventory 
          SET 
            quantity_available = quantity_available + $1,
            quantity_reserved = quantity_reserved - $1,
            last_updated = NOW()
          WHERE product_id = $2 AND fulfillment_center_id = $3
        `;

        await client.query(releaseQuery, [
          item.quantity,
          item.product_id,
          item.fulfillment_center_id
        ]);

        // Log inventory movement
        await this.logInventoryMovement(
          client,
          item.product_id,
          item.fulfillment_center_id,
          'in',
          item.quantity,
          'order',
          orderId,
          'Inventory released from cancelled order'
        );

        // Clear cache
        await this.clearInventoryCache(item.product_id, item.fulfillment_center_id);
      }

      logger.logInventory(null, null, null, 'inventory_released', {
        order_id: orderId,
        items_count: orderItemsResult.rows.length
      });
    });
  }

  /**
   * Fulfill order (move from reserved to sold)
   */
  async fulfillOrder(orderId) {
    return await transaction(async (client) => {
      // Get order items
      const orderItemsQuery = `
        SELECT oi.product_id, oi.quantity, o.fulfillment_center_id
        FROM order_items oi
        JOIN orders o ON oi.order_id = o.id
        WHERE o.id = $1
      `;

      const orderItemsResult = await client.query(orderItemsQuery, [orderId]);

      for (const item of orderItemsResult.rows) {
        // Update inventory (remove from reserved)
        const fulfillQuery = `
          UPDATE inventory 
          SET 
            quantity_reserved = quantity_reserved - $1,
            last_updated = NOW()
          WHERE product_id = $2 AND fulfillment_center_id = $3
        `;

        await client.query(fulfillQuery, [
          item.quantity,
          item.product_id,
          item.fulfillment_center_id
        ]);

        // Log inventory movement
        await this.logInventoryMovement(
          client,
          item.product_id,
          item.fulfillment_center_id,
          'out',
          item.quantity,
          'order',
          orderId,
          'Inventory fulfilled for order'
        );

        // Clear cache
        await this.clearInventoryCache(item.product_id, item.fulfillment_center_id);
      }

      logger.logInventory(null, null, null, 'inventory_fulfilled', {
        order_id: orderId,
        items_count: orderItemsResult.rows.length
      });
    });
  }

  /**
   * Update inventory levels
   */
  async updateInventory(productId, fulfillmentCenterId, quantityChange, reason = 'adjustment', referenceId = null) {
    return await transaction(async (client) => {
      const updateQuery = `
        UPDATE inventory 
        SET 
          quantity_available = quantity_available + $1,
          last_updated = NOW()
        WHERE product_id = $2 AND fulfillment_center_id = $3
        RETURNING *
      `;

      const result = await client.query(updateQuery, [
        quantityChange,
        productId,
        fulfillmentCenterId
      ]);

      if (result.rows.length === 0) {
        throw new Error('Inventory record not found');
      }

      // Log inventory movement
      const movementType = quantityChange > 0 ? 'in' : 'out';
      await this.logInventoryMovement(
        client,
        productId,
        fulfillmentCenterId,
        movementType,
        Math.abs(quantityChange),
        reason,
        referenceId,
        `Inventory ${reason}: ${quantityChange > 0 ? 'added' : 'removed'} ${Math.abs(quantityChange)} units`
      );

      // Clear cache
      await this.clearInventoryCache(productId, fulfillmentCenterId);

      // Check for low stock alerts
      await this.checkLowStockAlerts(productId, fulfillmentCenterId, result.rows[0]);

      logger.logInventory(productId, fulfillmentCenterId, quantityChange, reason, {
        reference_id: referenceId,
        new_quantity: result.rows[0].quantity_available
      });

      return result.rows[0];
    });
  }

  /**
   * Get inventory levels for a product across all fulfillment centers
   */
  async getProductInventory(productId) {
    const inventoryQuery = `
      SELECT 
        i.*,
        fc.name as fulfillment_center_name,
        fc.code as fulfillment_center_code,
        fc.address as fulfillment_center_address,
        p.name as product_name,
        p.sku as product_sku
      FROM inventory i
      JOIN fulfillment_centers fc ON i.fulfillment_center_id = fc.id
      JOIN products p ON i.product_id = p.id
      WHERE i.product_id = $1 AND fc.is_active = true
      ORDER BY fc.name
    `;

    const result = await query(inventoryQuery, [productId]);
    
    const totalAvailable = result.rows.reduce((sum, row) => sum + row.quantity_available, 0);
    const totalReserved = result.rows.reduce((sum, row) => sum + row.quantity_reserved, 0);

    return {
      product_id: productId,
      product_name: result.rows[0]?.product_name,
      product_sku: result.rows[0]?.product_sku,
      total_available: totalAvailable,
      total_reserved: totalReserved,
      locations: result.rows
    };
  }

  /**
   * Get low stock items
   */
  async getLowStockItems(fulfillmentCenterId = null) {
    let lowStockQuery = `
      SELECT 
        i.*,
        p.name as product_name,
        p.sku as product_sku,
        fc.name as fulfillment_center_name
      FROM inventory i
      JOIN products p ON i.product_id = p.id
      JOIN fulfillment_centers fc ON i.fulfillment_center_id = fc.id
      WHERE i.quantity_available <= i.reorder_point 
        AND p.is_active = true 
        AND fc.is_active = true
    `;

    const queryParams = [];

    if (fulfillmentCenterId) {
      lowStockQuery += ' AND i.fulfillment_center_id = $1';
      queryParams.push(fulfillmentCenterId);
    }

    lowStockQuery += ' ORDER BY i.quantity_available ASC';

    const result = await query(lowStockQuery, queryParams);
    return result.rows;
  }

  /**
   * Get inventory movements history
   */
  async getInventoryMovements(productId, fulfillmentCenterId = null, limit = 50) {
    let movementsQuery = `
      SELECT 
        im.*,
        p.name as product_name,
        p.sku as product_sku,
        fc.name as fulfillment_center_name
      FROM inventory_movements im
      JOIN products p ON im.product_id = p.id
      LEFT JOIN fulfillment_centers fc ON im.fulfillment_center_id = fc.id
      WHERE im.product_id = $1
    `;

    const queryParams = [productId];

    if (fulfillmentCenterId) {
      movementsQuery += ' AND im.fulfillment_center_id = $2';
      queryParams.push(fulfillmentCenterId);
    }

    movementsQuery += ' ORDER BY im.created_at DESC LIMIT $' + (queryParams.length + 1);
    queryParams.push(limit);

    const result = await query(movementsQuery, queryParams);
    return result.rows;
  }

  /**
   * Bulk inventory update
   */
  async bulkUpdateInventory(updates) {
    return await transaction(async (client) => {
      const results = [];

      for (const update of updates) {
        try {
          const result = await this.updateInventory(
            update.product_id,
            update.fulfillment_center_id,
            update.quantity_change,
            update.reason || 'bulk_update',
            update.reference_id
          );
          
          results.push({
            product_id: update.product_id,
            fulfillment_center_id: update.fulfillment_center_id,
            success: true,
            result
          });
        } catch (error) {
          results.push({
            product_id: update.product_id,
            fulfillment_center_id: update.fulfillment_center_id,
            success: false,
            error: error.message
          });
        }
      }

      return results;
    });
  }

  /**
   * Log inventory movement
   */
  async logInventoryMovement(client, productId, fulfillmentCenterId, movementType, quantity, referenceType, referenceId, notes) {
    const movementQuery = `
      INSERT INTO inventory_movements (
        id, product_id, fulfillment_center_id, movement_type, quantity,
        reference_type, reference_id, notes, created_at
      ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, NOW())
    `;

    await client.query(movementQuery, [
      uuidv4(),
      productId,
      fulfillmentCenterId,
      movementType,
      quantity,
      referenceType,
      referenceId,
      notes
    ]);
  }

  /**
   * Check for low stock alerts
   */
  async checkLowStockAlerts(productId, fulfillmentCenterId, inventory) {
    if (inventory.quantity_available <= inventory.reorder_point) {
      // Trigger low stock alert
      logger.warn('Low stock alert', {
        product_id: productId,
        fulfillment_center_id: fulfillmentCenterId,
        current_quantity: inventory.quantity_available,
        reorder_point: inventory.reorder_point
      });

      // Here you could trigger notifications, automatic reordering, etc.
      await this.triggerLowStockNotification(productId, fulfillmentCenterId, inventory);
    }
  }

  /**
   * Trigger low stock notification
   */
  async triggerLowStockNotification(productId, fulfillmentCenterId, inventory) {
    // Get product and fulfillment center details
    const detailsQuery = `
      SELECT 
        p.name as product_name,
        p.sku as product_sku,
        fc.name as fulfillment_center_name
      FROM products p, fulfillment_centers fc
      WHERE p.id = $1 AND fc.id = $2
    `;

    const result = await query(detailsQuery, [productId, fulfillmentCenterId]);
    
    if (result.rows.length > 0) {
      const details = result.rows[0];
      
      // Here you would integrate with notification service
      logger.info('Low stock notification triggered', {
        product_name: details.product_name,
        product_sku: details.product_sku,
        fulfillment_center: details.fulfillment_center_name,
        current_quantity: inventory.quantity_available,
        reorder_point: inventory.reorder_point
      });
    }
  }

  /**
   * Clear inventory cache
   */
  async clearInventoryCache(productId, fulfillmentCenterId) {
    const redis = require('../config/redis');
    await redis.del(redis.CACHE_KEYS.INVENTORY(productId, fulfillmentCenterId));
  }

  /**
   * Get inventory summary
   */
  async getInventorySummary() {
    const summaryQuery = `
      SELECT 
        COUNT(DISTINCT i.product_id) as total_products,
        COUNT(DISTINCT i.fulfillment_center_id) as total_fulfillment_centers,
        SUM(i.quantity_available) as total_available_quantity,
        SUM(i.quantity_reserved) as total_reserved_quantity,
        COUNT(CASE WHEN i.quantity_available <= i.reorder_point THEN 1 END) as low_stock_items
      FROM inventory i
      JOIN products p ON i.product_id = p.id
      JOIN fulfillment_centers fc ON i.fulfillment_center_id = fc.id
      WHERE p.is_active = true AND fc.is_active = true
    `;

    const result = await query(summaryQuery);
    return result.rows[0];
  }
}

module.exports = new InventoryService();
