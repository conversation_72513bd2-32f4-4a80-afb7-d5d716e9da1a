const { query, transaction } = require('../config/database');
const sendgridService = require('../integrations/sendgrid');
const twilioService = require('../integrations/twilio');
const logger = require('../utils/logger');
const { v4: uuidv4 } = require('uuid');

class NotificationService {
  constructor() {
    this.templates = {
      ORDER_CONFIRMATION: 'order_confirmation',
      ORDER_SHIPPED: 'order_shipped',
      ORDER_DELIVERED: 'order_delivered',
      ORDER_CANCELLED: 'order_cancelled',
      RETURN_APPROVED: 'return_approved',
      LOW_STOCK_ALERT: 'low_stock_alert',
      PAYMENT_FAILED: 'payment_failed'
    };
  }

  /**
   * Send order confirmation notification
   */
  async sendOrderConfirmation(order) {
    try {
      const customer = await this.getCustomerDetails(order.customer_id);
      
      // Send email confirmation
      const emailSent = await this.sendEmail({
        to: customer.email,
        template: this.templates.ORDER_CONFIRMATION,
        data: {
          customer_name: `${customer.first_name} ${customer.last_name}`,
          order_number: order.order_number,
          order_total: order.total_amount,
          order_items: order.items || [],
          shipping_address: order.shipping_address,
          estimated_delivery: this.calculateEstimatedDelivery(order)
        }
      });

      // Send SMS if customer has phone number
      let smsSent = false;
      if (customer.phone) {
        smsSent = await this.sendSMS({
          to: customer.phone,
          template: this.templates.ORDER_CONFIRMATION,
          data: {
            customer_name: customer.first_name,
            order_number: order.order_number,
            order_total: order.total_amount
          }
        });
      }

      // Log notifications
      await this.logNotification(
        customer.id,
        order.id,
        'email',
        this.templates.ORDER_CONFIRMATION,
        customer.email,
        emailSent ? 'sent' : 'failed'
      );

      if (customer.phone) {
        await this.logNotification(
          customer.id,
          order.id,
          'sms',
          this.templates.ORDER_CONFIRMATION,
          customer.phone,
          smsSent ? 'sent' : 'failed'
        );
      }

      logger.logNotification('email', customer.email, emailSent ? 'sent' : 'failed', {
        order_id: order.id,
        template: this.templates.ORDER_CONFIRMATION
      });

      return { emailSent, smsSent };

    } catch (error) {
      logger.error('Error sending order confirmation:', error);
      throw error;
    }
  }

  /**
   * Send shipping notification
   */
  async sendShippingNotification(order) {
    try {
      const customer = await this.getCustomerDetails(order.customer_id);
      
      const emailSent = await this.sendEmail({
        to: customer.email,
        template: this.templates.ORDER_SHIPPED,
        data: {
          customer_name: `${customer.first_name} ${customer.last_name}`,
          order_number: order.order_number,
          tracking_number: order.tracking_number,
          shipping_method: order.shipping_method,
          tracking_url: this.generateTrackingUrl(order.tracking_number, order.shipping_method),
          estimated_delivery: this.calculateEstimatedDelivery(order)
        }
      });

      let smsSent = false;
      if (customer.phone) {
        smsSent = await this.sendSMS({
          to: customer.phone,
          template: this.templates.ORDER_SHIPPED,
          data: {
            customer_name: customer.first_name,
            order_number: order.order_number,
            tracking_number: order.tracking_number
          }
        });
      }

      // Log notifications
      await this.logNotification(
        customer.id,
        order.id,
        'email',
        this.templates.ORDER_SHIPPED,
        customer.email,
        emailSent ? 'sent' : 'failed'
      );

      if (customer.phone) {
        await this.logNotification(
          customer.id,
          order.id,
          'sms',
          this.templates.ORDER_SHIPPED,
          customer.phone,
          smsSent ? 'sent' : 'failed'
        );
      }

      return { emailSent, smsSent };

    } catch (error) {
      logger.error('Error sending shipping notification:', error);
      throw error;
    }
  }

  /**
   * Send delivery confirmation
   */
  async sendDeliveryConfirmation(order) {
    try {
      const customer = await this.getCustomerDetails(order.customer_id);
      
      const emailSent = await this.sendEmail({
        to: customer.email,
        template: this.templates.ORDER_DELIVERED,
        data: {
          customer_name: `${customer.first_name} ${customer.last_name}`,
          order_number: order.order_number,
          delivery_date: new Date().toLocaleDateString(),
          return_policy_url: process.env.RETURN_POLICY_URL || '#',
          support_email: process.env.SUPPORT_EMAIL || '<EMAIL>'
        }
      });

      let smsSent = false;
      if (customer.phone) {
        smsSent = await this.sendSMS({
          to: customer.phone,
          template: this.templates.ORDER_DELIVERED,
          data: {
            customer_name: customer.first_name,
            order_number: order.order_number
          }
        });
      }

      // Log notifications
      await this.logNotification(
        customer.id,
        order.id,
        'email',
        this.templates.ORDER_DELIVERED,
        customer.email,
        emailSent ? 'sent' : 'failed'
      );

      if (customer.phone) {
        await this.logNotification(
          customer.id,
          order.id,
          'sms',
          this.templates.ORDER_DELIVERED,
          customer.phone,
          smsSent ? 'sent' : 'failed'
        );
      }

      return { emailSent, smsSent };

    } catch (error) {
      logger.error('Error sending delivery confirmation:', error);
      throw error;
    }
  }

  /**
   * Send order cancellation notification
   */
  async sendOrderCancellation(order) {
    try {
      const customer = await this.getCustomerDetails(order.customer_id);
      
      const emailSent = await this.sendEmail({
        to: customer.email,
        template: this.templates.ORDER_CANCELLED,
        data: {
          customer_name: `${customer.first_name} ${customer.last_name}`,
          order_number: order.order_number,
          cancellation_date: new Date().toLocaleDateString(),
          refund_amount: order.total_amount,
          refund_timeline: '3-5 business days',
          support_email: process.env.SUPPORT_EMAIL || '<EMAIL>'
        }
      });

      let smsSent = false;
      if (customer.phone) {
        smsSent = await this.sendSMS({
          to: customer.phone,
          template: this.templates.ORDER_CANCELLED,
          data: {
            customer_name: customer.first_name,
            order_number: order.order_number
          }
        });
      }

      // Log notifications
      await this.logNotification(
        customer.id,
        order.id,
        'email',
        this.templates.ORDER_CANCELLED,
        customer.email,
        emailSent ? 'sent' : 'failed'
      );

      if (customer.phone) {
        await this.logNotification(
          customer.id,
          order.id,
          'sms',
          this.templates.ORDER_CANCELLED,
          customer.phone,
          smsSent ? 'sent' : 'failed'
        );
      }

      return { emailSent, smsSent };

    } catch (error) {
      logger.error('Error sending cancellation notification:', error);
      throw error;
    }
  }

  /**
   * Send return approval notification
   */
  async sendReturnApproval(returnRequest) {
    try {
      const customer = await this.getCustomerDetails(returnRequest.customer_id);
      const order = await this.getOrderDetails(returnRequest.order_id);
      
      const emailSent = await this.sendEmail({
        to: customer.email,
        template: this.templates.RETURN_APPROVED,
        data: {
          customer_name: `${customer.first_name} ${customer.last_name}`,
          return_number: returnRequest.return_number,
          order_number: order.order_number,
          return_items: returnRequest.items || [],
          return_shipping_label: this.generateReturnShippingLabel(returnRequest),
          refund_amount: returnRequest.refund_amount
        }
      });

      // Log notification
      await this.logNotification(
        customer.id,
        returnRequest.order_id,
        'email',
        this.templates.RETURN_APPROVED,
        customer.email,
        emailSent ? 'sent' : 'failed'
      );

      return { emailSent };

    } catch (error) {
      logger.error('Error sending return approval:', error);
      throw error;
    }
  }

  /**
   * Send low stock alert to administrators
   */
  async sendLowStockAlert(product, fulfillmentCenter, currentStock) {
    try {
      // Get admin email addresses
      const adminEmails = await this.getAdminEmails();
      
      for (const adminEmail of adminEmails) {
        const emailSent = await this.sendEmail({
          to: adminEmail,
          template: this.templates.LOW_STOCK_ALERT,
          data: {
            product_name: product.name,
            product_sku: product.sku,
            fulfillment_center: fulfillmentCenter.name,
            current_stock: currentStock,
            reorder_point: product.reorder_point,
            suggested_reorder_quantity: product.max_stock_level - currentStock
          }
        });

        // Log notification
        await this.logNotification(
          null, // No specific customer
          null, // No specific order
          'email',
          this.templates.LOW_STOCK_ALERT,
          adminEmail,
          emailSent ? 'sent' : 'failed'
        );
      }

    } catch (error) {
      logger.error('Error sending low stock alert:', error);
      throw error;
    }
  }

  /**
   * Send email notification
   */
  async sendEmail({ to, template, data }) {
    try {
      const emailContent = this.generateEmailContent(template, data);
      
      const result = await sendgridService.sendEmail({
        to,
        subject: emailContent.subject,
        html: emailContent.html,
        text: emailContent.text
      });

      logger.logNotification('email', to, 'sent', {
        template,
        message_id: result.messageId
      });

      return true;

    } catch (error) {
      logger.error('Error sending email:', {
        to,
        template,
        error: error.message
      });
      return false;
    }
  }

  /**
   * Send SMS notification
   */
  async sendSMS({ to, template, data }) {
    try {
      const smsContent = this.generateSMSContent(template, data);
      
      const result = await twilioService.sendSMS({
        to,
        body: smsContent
      });

      logger.logNotification('sms', to, 'sent', {
        template,
        message_sid: result.sid
      });

      return true;

    } catch (error) {
      logger.error('Error sending SMS:', {
        to,
        template,
        error: error.message
      });
      return false;
    }
  }

  /**
   * Generate email content based on template
   */
  generateEmailContent(template, data) {
    const templates = {
      [this.templates.ORDER_CONFIRMATION]: {
        subject: `Order Confirmation - ${data.order_number}`,
        html: this.getOrderConfirmationHTML(data),
        text: this.getOrderConfirmationText(data)
      },
      [this.templates.ORDER_SHIPPED]: {
        subject: `Your Order Has Shipped - ${data.order_number}`,
        html: this.getOrderShippedHTML(data),
        text: this.getOrderShippedText(data)
      },
      [this.templates.ORDER_DELIVERED]: {
        subject: `Order Delivered - ${data.order_number}`,
        html: this.getOrderDeliveredHTML(data),
        text: this.getOrderDeliveredText(data)
      },
      [this.templates.ORDER_CANCELLED]: {
        subject: `Order Cancelled - ${data.order_number}`,
        html: this.getOrderCancelledHTML(data),
        text: this.getOrderCancelledText(data)
      },
      [this.templates.RETURN_APPROVED]: {
        subject: `Return Approved - ${data.return_number}`,
        html: this.getReturnApprovedHTML(data),
        text: this.getReturnApprovedText(data)
      },
      [this.templates.LOW_STOCK_ALERT]: {
        subject: `Low Stock Alert - ${data.product_name}`,
        html: this.getLowStockAlertHTML(data),
        text: this.getLowStockAlertText(data)
      }
    };

    return templates[template] || {
      subject: 'Notification',
      html: '<p>Notification content</p>',
      text: 'Notification content'
    };
  }

  /**
   * Generate SMS content based on template
   */
  generateSMSContent(template, data) {
    const templates = {
      [this.templates.ORDER_CONFIRMATION]: 
        `Hi ${data.customer_name}! Your order ${data.order_number} for $${data.order_total} has been confirmed. Thank you for your purchase!`,
      
      [this.templates.ORDER_SHIPPED]: 
        `Hi ${data.customer_name}! Your order ${data.order_number} has shipped. Tracking: ${data.tracking_number}`,
      
      [this.templates.ORDER_DELIVERED]: 
        `Hi ${data.customer_name}! Your order ${data.order_number} has been delivered. Thank you for choosing us!`,
      
      [this.templates.ORDER_CANCELLED]: 
        `Hi ${data.customer_name}, your order ${data.order_number} has been cancelled. Refund will be processed within 3-5 business days.`
    };

    return templates[template] || 'Notification from OrderFlow Pro';
  }

  /**
   * Helper methods for email templates
   */
  getOrderConfirmationHTML(data) {
    return `
      <h2>Order Confirmation</h2>
      <p>Dear ${data.customer_name},</p>
      <p>Thank you for your order! Your order ${data.order_number} has been confirmed.</p>
      <p><strong>Order Total:</strong> $${data.order_total}</p>
      <p><strong>Estimated Delivery:</strong> ${data.estimated_delivery}</p>
      <p>We'll send you another email when your order ships.</p>
    `;
  }

  getOrderConfirmationText(data) {
    return `Order Confirmation\n\nDear ${data.customer_name},\n\nThank you for your order! Your order ${data.order_number} has been confirmed.\n\nOrder Total: $${data.order_total}\nEstimated Delivery: ${data.estimated_delivery}\n\nWe'll send you another email when your order ships.`;
  }

  getOrderShippedHTML(data) {
    return `
      <h2>Your Order Has Shipped!</h2>
      <p>Dear ${data.customer_name},</p>
      <p>Great news! Your order ${data.order_number} has shipped.</p>
      <p><strong>Tracking Number:</strong> ${data.tracking_number}</p>
      <p><strong>Shipping Method:</strong> ${data.shipping_method}</p>
      <p><a href="${data.tracking_url}">Track Your Package</a></p>
      <p><strong>Estimated Delivery:</strong> ${data.estimated_delivery}</p>
    `;
  }

  getOrderShippedText(data) {
    return `Your Order Has Shipped!\n\nDear ${data.customer_name},\n\nGreat news! Your order ${data.order_number} has shipped.\n\nTracking Number: ${data.tracking_number}\nShipping Method: ${data.shipping_method}\nEstimated Delivery: ${data.estimated_delivery}`;
  }

  getOrderDeliveredHTML(data) {
    return `
      <h2>Order Delivered</h2>
      <p>Dear ${data.customer_name},</p>
      <p>Your order ${data.order_number} has been delivered on ${data.delivery_date}.</p>
      <p>We hope you're happy with your purchase!</p>
      <p>If you have any questions, please contact us at ${data.support_email}</p>
    `;
  }

  getOrderDeliveredText(data) {
    return `Order Delivered\n\nDear ${data.customer_name},\n\nYour order ${data.order_number} has been delivered on ${data.delivery_date}.\n\nWe hope you're happy with your purchase!\n\nIf you have any questions, please contact us at ${data.support_email}`;
  }

  getOrderCancelledHTML(data) {
    return `
      <h2>Order Cancelled</h2>
      <p>Dear ${data.customer_name},</p>
      <p>Your order ${data.order_number} has been cancelled on ${data.cancellation_date}.</p>
      <p><strong>Refund Amount:</strong> $${data.refund_amount}</p>
      <p>Your refund will be processed within ${data.refund_timeline}.</p>
      <p>If you have any questions, please contact us at ${data.support_email}</p>
    `;
  }

  getOrderCancelledText(data) {
    return `Order Cancelled\n\nDear ${data.customer_name},\n\nYour order ${data.order_number} has been cancelled on ${data.cancellation_date}.\n\nRefund Amount: $${data.refund_amount}\nYour refund will be processed within ${data.refund_timeline}.\n\nIf you have any questions, please contact us at ${data.support_email}`;
  }

  getReturnApprovedHTML(data) {
    return `
      <h2>Return Approved</h2>
      <p>Dear ${data.customer_name},</p>
      <p>Your return request ${data.return_number} for order ${data.order_number} has been approved.</p>
      <p><strong>Refund Amount:</strong> $${data.refund_amount}</p>
      <p>Please use the attached return shipping label to send back your items.</p>
    `;
  }

  getReturnApprovedText(data) {
    return `Return Approved\n\nDear ${data.customer_name},\n\nYour return request ${data.return_number} for order ${data.order_number} has been approved.\n\nRefund Amount: $${data.refund_amount}\n\nPlease use the return shipping label to send back your items.`;
  }

  getLowStockAlertHTML(data) {
    return `
      <h2>Low Stock Alert</h2>
      <p><strong>Product:</strong> ${data.product_name} (${data.product_sku})</p>
      <p><strong>Fulfillment Center:</strong> ${data.fulfillment_center}</p>
      <p><strong>Current Stock:</strong> ${data.current_stock}</p>
      <p><strong>Reorder Point:</strong> ${data.reorder_point}</p>
      <p><strong>Suggested Reorder Quantity:</strong> ${data.suggested_reorder_quantity}</p>
    `;
  }

  getLowStockAlertText(data) {
    return `Low Stock Alert\n\nProduct: ${data.product_name} (${data.product_sku})\nFulfillment Center: ${data.fulfillment_center}\nCurrent Stock: ${data.current_stock}\nReorder Point: ${data.reorder_point}\nSuggested Reorder Quantity: ${data.suggested_reorder_quantity}`;
  }

  /**
   * Helper methods
   */
  async getCustomerDetails(customerId) {
    const customerQuery = `
      SELECT * FROM customers WHERE id = $1
    `;
    const result = await query(customerQuery, [customerId]);
    return result.rows[0];
  }

  async getOrderDetails(orderId) {
    const orderQuery = `
      SELECT * FROM orders WHERE id = $1
    `;
    const result = await query(orderQuery, [orderId]);
    return result.rows[0];
  }

  async getAdminEmails() {
    // In a real implementation, this would fetch from a configuration table
    return [
      process.env.ADMIN_EMAIL || '<EMAIL>',
      process.env.INVENTORY_MANAGER_EMAIL || '<EMAIL>'
    ];
  }

  calculateEstimatedDelivery(order) {
    // Simple estimation - in real implementation, integrate with shipping carriers
    const businessDays = 3;
    const deliveryDate = new Date();
    deliveryDate.setDate(deliveryDate.getDate() + businessDays);
    return deliveryDate.toLocaleDateString();
  }

  generateTrackingUrl(trackingNumber, shippingMethod) {
    const trackingUrls = {
      'UPS': `https://www.ups.com/track?tracknum=${trackingNumber}`,
      'FedEx': `https://www.fedex.com/fedextrack/?tracknumbers=${trackingNumber}`,
      'USPS': `https://tools.usps.com/go/TrackConfirmAction?qtc_tLabels1=${trackingNumber}`
    };

    return trackingUrls[shippingMethod] || `#track-${trackingNumber}`;
  }

  generateReturnShippingLabel(returnRequest) {
    // In a real implementation, this would generate an actual shipping label
    return `Return shipping label for ${returnRequest.return_number}`;
  }

  async logNotification(customerId, orderId, type, template, recipient, status) {
    const logQuery = `
      INSERT INTO notifications (
        id, customer_id, order_id, type, subject, message, recipient, status, created_at
      ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, NOW())
    `;

    await query(logQuery, [
      uuidv4(),
      customerId,
      orderId,
      type,
      template,
      `${template} notification`,
      recipient,
      status
    ]);
  }
}

module.exports = new NotificationService();
