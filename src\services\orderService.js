const { query, transaction } = require('../config/database');
const { cacheOrder, getCachedOrder } = require('../config/redis');
const logger = require('../utils/logger');
const { v4: uuidv4 } = require('uuid');

class OrderService {
  /**
   * Get orders with filtering and pagination
   */
  async getOrders(filters = {}, pagination = { page: 1, limit: 20 }) {
    const { page, limit } = pagination;
    const offset = (page - 1) * limit;

    let whereConditions = ['1=1'];
    let queryParams = [];
    let paramIndex = 1;

    // Build dynamic WHERE clause
    if (filters.status) {
      whereConditions.push(`o.status = $${paramIndex++}`);
      queryParams.push(filters.status);
    }

    if (filters.payment_status) {
      whereConditions.push(`o.payment_status = $${paramIndex++}`);
      queryParams.push(filters.payment_status);
    }

    if (filters.channel_id) {
      whereConditions.push(`o.channel_id = $${paramIndex++}`);
      queryParams.push(filters.channel_id);
    }

    if (filters.customer_id) {
      whereConditions.push(`o.customer_id = $${paramIndex++}`);
      queryParams.push(filters.customer_id);
    }

    if (filters.date_from) {
      whereConditions.push(`o.created_at >= $${paramIndex++}`);
      queryParams.push(filters.date_from);
    }

    if (filters.date_to) {
      whereConditions.push(`o.created_at <= $${paramIndex++}`);
      queryParams.push(filters.date_to);
    }

    if (filters.search) {
      whereConditions.push(`(o.order_number ILIKE $${paramIndex} OR c.email ILIKE $${paramIndex} OR c.first_name ILIKE $${paramIndex} OR c.last_name ILIKE $${paramIndex})`);
      queryParams.push(`%${filters.search}%`);
      paramIndex++;
    }

    const whereClause = whereConditions.join(' AND ');

    // Main query
    const ordersQuery = `
      SELECT 
        o.*,
        c.email as customer_email,
        c.first_name as customer_first_name,
        c.last_name as customer_last_name,
        sc.name as channel_name,
        fc.name as fulfillment_center_name
      FROM orders o
      LEFT JOIN customers c ON o.customer_id = c.id
      LEFT JOIN sales_channels sc ON o.channel_id = sc.id
      LEFT JOIN fulfillment_centers fc ON o.fulfillment_center_id = fc.id
      WHERE ${whereClause}
      ORDER BY o.created_at DESC
      LIMIT $${paramIndex++} OFFSET $${paramIndex++}
    `;

    queryParams.push(limit, offset);

    // Count query
    const countQuery = `
      SELECT COUNT(*) as total
      FROM orders o
      LEFT JOIN customers c ON o.customer_id = c.id
      WHERE ${whereClause}
    `;

    const [ordersResult, countResult] = await Promise.all([
      query(ordersQuery, queryParams.slice(0, -2)), // Remove limit and offset for count
      query(countQuery, queryParams.slice(0, -2))
    ]);

    const total = parseInt(countResult.rows[0].total);
    const totalPages = Math.ceil(total / limit);

    return {
      orders: ordersResult.rows,
      pagination: {
        page,
        limit,
        total,
        totalPages,
        hasNext: page < totalPages,
        hasPrev: page > 1
      }
    };
  }

  /**
   * Get order by ID
   */
  async getOrderById(orderId) {
    // Try cache first
    const cached = await getCachedOrder(orderId);
    if (cached) {
      return cached;
    }

    const orderQuery = `
      SELECT 
        o.*,
        c.email as customer_email,
        c.first_name as customer_first_name,
        c.last_name as customer_last_name,
        c.phone as customer_phone,
        sc.name as channel_name,
        fc.name as fulfillment_center_name,
        fc.address as fulfillment_center_address
      FROM orders o
      LEFT JOIN customers c ON o.customer_id = c.id
      LEFT JOIN sales_channels sc ON o.channel_id = sc.id
      LEFT JOIN fulfillment_centers fc ON o.fulfillment_center_id = fc.id
      WHERE o.id = $1
    `;

    const itemsQuery = `
      SELECT 
        oi.*,
        p.name as product_name,
        p.sku as product_sku
      FROM order_items oi
      LEFT JOIN products p ON oi.product_id = p.id
      WHERE oi.order_id = $1
      ORDER BY oi.created_at
    `;

    const [orderResult, itemsResult] = await Promise.all([
      query(orderQuery, [orderId]),
      query(itemsQuery, [orderId])
    ]);

    if (orderResult.rows.length === 0) {
      return null;
    }

    const order = {
      ...orderResult.rows[0],
      items: itemsResult.rows
    };

    // Cache the order
    await cacheOrder(orderId, order, 1800); // 30 minutes

    return order;
  }

  /**
   * Create new order
   */
  async createOrder(orderData) {
    return await transaction(async (client) => {
      const orderId = uuidv4();
      const orderNumber = await this.generateOrderNumber();

      // Calculate totals
      const subtotal = orderData.items.reduce((sum, item) => 
        sum + (item.quantity * item.unit_price), 0
      );
      
      const taxAmount = orderData.tax_amount || (subtotal * 0.08); // 8% default tax
      const shippingAmount = orderData.shipping_amount || 9.99;
      const discountAmount = orderData.discount_amount || 0;
      const totalAmount = subtotal + taxAmount + shippingAmount - discountAmount;

      // Insert order
      const orderQuery = `
        INSERT INTO orders (
          id, order_number, customer_id, channel_id, status, payment_status,
          subtotal, tax_amount, shipping_amount, discount_amount, total_amount,
          currency, billing_address, shipping_address, fulfillment_center_id,
          shipping_method, notes, created_at, updated_at
        ) VALUES (
          $1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16, $17, $18, $19
        ) RETURNING *
      `;

      const orderValues = [
        orderId,
        orderNumber,
        orderData.customer_id,
        orderData.channel_id,
        'pending',
        'pending',
        subtotal,
        taxAmount,
        shippingAmount,
        discountAmount,
        totalAmount,
        orderData.currency || 'USD',
        JSON.stringify(orderData.billing_address),
        JSON.stringify(orderData.shipping_address),
        orderData.fulfillment_center_id,
        orderData.shipping_method,
        orderData.notes,
        new Date(),
        new Date()
      ];

      const orderResult = await client.query(orderQuery, orderValues);

      // Insert order items
      for (const item of orderData.items) {
        const itemQuery = `
          INSERT INTO order_items (
            id, order_id, product_id, quantity, unit_price, total_price, created_at
          ) VALUES ($1, $2, $3, $4, $5, $6, $7)
        `;

        const itemValues = [
          uuidv4(),
          orderId,
          item.product_id,
          item.quantity,
          item.unit_price,
          item.quantity * item.unit_price,
          new Date()
        ];

        await client.query(itemQuery, itemValues);
      }

      return await this.getOrderById(orderId);
    });
  }

  /**
   * Update order
   */
  async updateOrder(orderId, updates) {
    const setClause = [];
    const values = [];
    let paramIndex = 1;

    // Build dynamic SET clause
    for (const [key, value] of Object.entries(updates)) {
      if (value !== undefined) {
        setClause.push(`${key} = $${paramIndex++}`);
        values.push(value);
      }
    }

    if (setClause.length === 0) {
      throw new Error('No valid updates provided');
    }

    setClause.push(`updated_at = $${paramIndex++}`);
    values.push(new Date());
    values.push(orderId);

    const updateQuery = `
      UPDATE orders 
      SET ${setClause.join(', ')}
      WHERE id = $${paramIndex}
      RETURNING *
    `;

    const result = await query(updateQuery, values);
    
    if (result.rows.length === 0) {
      throw new Error('Order not found');
    }

    // Clear cache
    await this.clearOrderCache(orderId);

    return await this.getOrderById(orderId);
  }

  /**
   * Process order (move to next stage)
   */
  async processOrder(orderId) {
    const order = await this.getOrderById(orderId);
    if (!order) {
      throw new Error('Order not found');
    }

    const nextStatus = this.getNextStatus(order.status);
    if (!nextStatus) {
      throw new Error('Order cannot be processed further');
    }

    return await this.updateOrder(orderId, { status: nextStatus });
  }

  /**
   * Get next status in order workflow
   */
  getNextStatus(currentStatus) {
    const statusFlow = {
      'pending': 'confirmed',
      'confirmed': 'processing',
      'processing': 'shipped',
      'shipped': 'delivered'
    };

    return statusFlow[currentStatus] || null;
  }

  /**
   * Check if status transition is valid
   */
  isValidStatusTransition(fromStatus, toStatus) {
    const validTransitions = {
      'pending': ['confirmed', 'cancelled'],
      'confirmed': ['processing', 'cancelled'],
      'processing': ['shipped', 'cancelled'],
      'shipped': ['delivered', 'returned'],
      'delivered': ['returned'],
      'cancelled': [],
      'returned': []
    };

    return validTransitions[fromStatus]?.includes(toStatus) || false;
  }

  /**
   * Generate unique order number
   */
  async generateOrderNumber() {
    const prefix = 'ORD';
    const timestamp = Date.now().toString().slice(-8);
    const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0');
    
    return `${prefix}-${timestamp}-${random}`;
  }

  /**
   * Get order tracking information
   */
  async getOrderTracking(orderId) {
    const trackingQuery = `
      SELECT 
        o.id,
        o.order_number,
        o.status,
        o.tracking_number,
        o.shipping_method,
        o.created_at,
        o.updated_at,
        fc.name as fulfillment_center_name,
        fc.address as fulfillment_center_address
      FROM orders o
      LEFT JOIN fulfillment_centers fc ON o.fulfillment_center_id = fc.id
      WHERE o.id = $1
    `;

    const result = await query(trackingQuery, [orderId]);
    return result.rows[0] || null;
  }

  /**
   * Get orders by customer
   */
  async getOrdersByCustomer(customerId, pagination = { page: 1, limit: 20 }) {
    return await this.getOrders({ customer_id: customerId }, pagination);
  }

  /**
   * Get orders by channel
   */
  async getOrdersByChannel(channelId, pagination = { page: 1, limit: 20 }) {
    return await this.getOrders({ channel_id: channelId }, pagination);
  }

  /**
   * Bulk update orders
   */
  async bulkUpdateOrders(orderIds, updates) {
    return await transaction(async (client) => {
      const setClause = [];
      const values = [];
      let paramIndex = 1;

      // Build dynamic SET clause
      for (const [key, value] of Object.entries(updates)) {
        if (value !== undefined) {
          setClause.push(`${key} = $${paramIndex++}`);
          values.push(value);
        }
      }

      setClause.push(`updated_at = $${paramIndex++}`);
      values.push(new Date());

      // Add order IDs as array parameter
      values.push(orderIds);

      const updateQuery = `
        UPDATE orders 
        SET ${setClause.join(', ')}
        WHERE id = ANY($${paramIndex})
        RETURNING id
      `;

      const result = await client.query(updateQuery, values);

      // Clear cache for updated orders
      for (const orderId of orderIds) {
        await this.clearOrderCache(orderId);
      }

      return {
        updated_count: result.rows.length,
        updated_ids: result.rows.map(row => row.id)
      };
    });
  }

  /**
   * Export orders to CSV
   */
  async exportOrdersToCSV(filters = {}) {
    const { orders } = await this.getOrders(filters, { page: 1, limit: 10000 });

    const headers = [
      'Order Number', 'Customer Email', 'Status', 'Payment Status',
      'Total Amount', 'Channel', 'Created At', 'Updated At'
    ];

    const csvRows = [headers.join(',')];

    for (const order of orders) {
      const row = [
        order.order_number,
        order.customer_email,
        order.status,
        order.payment_status,
        order.total_amount,
        order.channel_name,
        order.created_at,
        order.updated_at
      ];
      csvRows.push(row.join(','));
    }

    return csvRows.join('\n');
  }

  /**
   * Process webhook order from external channel
   */
  async processWebhookOrder(channelId, webhookData) {
    // Transform webhook data to internal order format
    const orderData = this.transformWebhookData(channelId, webhookData);
    
    // Create order
    return await this.createOrder(orderData);
  }

  /**
   * Transform webhook data to internal format
   */
  transformWebhookData(channelId, webhookData) {
    // This would be customized based on each channel's webhook format
    // For now, assume a generic format
    return {
      customer_id: webhookData.customer_id,
      channel_id: channelId,
      items: webhookData.items,
      billing_address: webhookData.billing_address,
      shipping_address: webhookData.shipping_address,
      shipping_method: webhookData.shipping_method,
      notes: webhookData.notes
    };
  }

  /**
   * Clear order cache
   */
  async clearOrderCache(orderId) {
    const redis = require('../config/redis');
    await redis.del(redis.CACHE_KEYS.ORDER(orderId));
  }
}

module.exports = new OrderService();
