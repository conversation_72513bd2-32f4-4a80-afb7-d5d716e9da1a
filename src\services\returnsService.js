const { query, transaction } = require('../config/database');
const inventoryService = require('./inventoryService');
const notificationService = require('./notificationService');
const logger = require('../utils/logger');
const { v4: uuidv4 } = require('uuid');

class ReturnsService {
  constructor() {
    this.returnStatuses = {
      REQUESTED: 'requested',
      APPROVED: 'approved',
      REJECTED: 'rejected',
      RECEIVED: 'received',
      PROCESSED: 'processed'
    };

    this.returnReasons = {
      DEFECTIVE: 'defective',
      WRONG_ITEM: 'wrong_item',
      NOT_AS_DESCRIBED: 'not_as_described',
      DAMAGED_SHIPPING: 'damaged_shipping',
      CHANGED_MIND: 'changed_mind',
      SIZE_ISSUE: 'size_issue',
      OTHER: 'other'
    };
  }

  /**
   * Create return request
   */
  async createReturnRequest(returnData) {
    return await transaction(async (client) => {
      const returnId = uuidv4();
      const returnNumber = await this.generateReturnNumber();

      // Validate order and items
      await this.validateReturnRequest(returnData);

      // Calculate refund amount
      const refundAmount = await this.calculateRefundAmount(returnData);

      // Create return record
      const returnQuery = `
        INSERT INTO returns (
          id, return_number, order_id, customer_id, status, reason,
          refund_amount, restocking_fee, created_at, updated_at
        ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, NOW(), NOW())
        RETURNING *
      `;

      const returnValues = [
        returnId,
        returnNumber,
        returnData.order_id,
        returnData.customer_id,
        this.returnStatuses.REQUESTED,
        returnData.reason,
        refundAmount.total,
        refundAmount.restockingFee,
      ];

      const returnResult = await client.query(returnQuery, returnValues);

      // Create return items
      for (const item of returnData.items) {
        const returnItemQuery = `
          INSERT INTO return_items (
            id, return_id, order_item_id, quantity, condition, created_at
          ) VALUES ($1, $2, $3, $4, $5, NOW())
        `;

        await client.query(returnItemQuery, [
          uuidv4(),
          returnId,
          item.order_item_id,
          item.quantity,
          item.condition || 'used'
        ]);
      }

      const returnRequest = returnResult.rows[0];

      // Send notification to customer
      await notificationService.sendReturnRequestConfirmation(returnRequest);

      logger.info('Return request created', {
        return_id: returnId,
        return_number: returnNumber,
        order_id: returnData.order_id,
        customer_id: returnData.customer_id,
        refund_amount: refundAmount.total
      });

      return await this.getReturnById(returnId);
    });
  }

  /**
   * Approve return request
   */
  async approveReturn(returnId, approvedBy) {
    return await transaction(async (client) => {
      // Get return details
      const returnRequest = await this.getReturnById(returnId);
      
      if (!returnRequest) {
        throw new Error('Return request not found');
      }

      if (returnRequest.status !== this.returnStatuses.REQUESTED) {
        throw new Error('Return request cannot be approved in current status');
      }

      // Update return status
      const updateQuery = `
        UPDATE returns 
        SET status = $1, updated_at = NOW()
        WHERE id = $2
        RETURNING *
      `;

      await client.query(updateQuery, [this.returnStatuses.APPROVED, returnId]);

      // Generate return shipping label
      const shippingLabel = await this.generateReturnShippingLabel(returnRequest);

      // Send approval notification
      await notificationService.sendReturnApproval({
        ...returnRequest,
        shipping_label: shippingLabel,
        approved_by: approvedBy
      });

      logger.info('Return request approved', {
        return_id: returnId,
        return_number: returnRequest.return_number,
        approved_by: approvedBy
      });

      return await this.getReturnById(returnId);
    });
  }

  /**
   * Reject return request
   */
  async rejectReturn(returnId, rejectionReason, rejectedBy) {
    return await transaction(async (client) => {
      const returnRequest = await this.getReturnById(returnId);
      
      if (!returnRequest) {
        throw new Error('Return request not found');
      }

      if (returnRequest.status !== this.returnStatuses.REQUESTED) {
        throw new Error('Return request cannot be rejected in current status');
      }

      // Update return status
      const updateQuery = `
        UPDATE returns 
        SET status = $1, updated_at = NOW()
        WHERE id = $2
        RETURNING *
      `;

      await client.query(updateQuery, [this.returnStatuses.REJECTED, returnId]);

      // Send rejection notification
      await notificationService.sendReturnRejection({
        ...returnRequest,
        rejection_reason: rejectionReason,
        rejected_by: rejectedBy
      });

      logger.info('Return request rejected', {
        return_id: returnId,
        return_number: returnRequest.return_number,
        rejection_reason: rejectionReason,
        rejected_by: rejectedBy
      });

      return await this.getReturnById(returnId);
    });
  }

  /**
   * Process received return
   */
  async processReceivedReturn(returnId, receivedItems, processedBy) {
    return await transaction(async (client) => {
      const returnRequest = await this.getReturnById(returnId);
      
      if (!returnRequest) {
        throw new Error('Return request not found');
      }

      if (returnRequest.status !== this.returnStatuses.APPROVED) {
        throw new Error('Return must be approved before processing');
      }

      // Update return status to received
      await client.query(
        'UPDATE returns SET status = $1, updated_at = NOW() WHERE id = $2',
        [this.returnStatuses.RECEIVED, returnId]
      );

      // Process each received item
      for (const receivedItem of receivedItems) {
        await this.processReturnItem(client, returnRequest, receivedItem);
      }

      // Calculate final refund amount based on received items
      const finalRefundAmount = await this.calculateFinalRefundAmount(returnRequest, receivedItems);

      // Update return with final refund amount
      await client.query(
        'UPDATE returns SET refund_amount = $1, status = $2, updated_at = NOW() WHERE id = $3',
        [finalRefundAmount, this.returnStatuses.PROCESSED, returnId]
      );

      // Process refund
      await this.processRefund(returnRequest, finalRefundAmount);

      // Send processing completion notification
      await notificationService.sendReturnProcessed({
        ...returnRequest,
        final_refund_amount: finalRefundAmount,
        processed_by: processedBy
      });

      logger.info('Return processed', {
        return_id: returnId,
        return_number: returnRequest.return_number,
        final_refund_amount: finalRefundAmount,
        processed_by: processedBy
      });

      return await this.getReturnById(returnId);
    });
  }

  /**
   * Process individual return item
   */
  async processReturnItem(client, returnRequest, receivedItem) {
    // Get order item details
    const orderItemQuery = `
      SELECT oi.*, p.name as product_name, p.sku as product_sku
      FROM order_items oi
      JOIN products p ON oi.product_id = p.id
      WHERE oi.id = $1
    `;

    const orderItemResult = await client.query(orderItemQuery, [receivedItem.order_item_id]);
    const orderItem = orderItemResult.rows[0];

    if (!orderItem) {
      throw new Error(`Order item ${receivedItem.order_item_id} not found`);
    }

    // Determine if item can be restocked based on condition
    const canRestock = this.canRestockItem(receivedItem.condition);

    if (canRestock) {
      // Add back to inventory
      await inventoryService.updateInventory(
        orderItem.product_id,
        returnRequest.fulfillment_center_id,
        receivedItem.quantity,
        'return',
        returnRequest.id
      );

      logger.info('Item restocked from return', {
        return_id: returnRequest.id,
        product_id: orderItem.product_id,
        quantity: receivedItem.quantity,
        condition: receivedItem.condition
      });
    } else {
      // Mark as damaged/unsellable
      logger.info('Item marked as unsellable', {
        return_id: returnRequest.id,
        product_id: orderItem.product_id,
        quantity: receivedItem.quantity,
        condition: receivedItem.condition
      });
    }
  }

  /**
   * Get return by ID
   */
  async getReturnById(returnId) {
    const returnQuery = `
      SELECT 
        r.*,
        o.order_number,
        o.fulfillment_center_id,
        c.email as customer_email,
        c.first_name as customer_first_name,
        c.last_name as customer_last_name
      FROM returns r
      JOIN orders o ON r.order_id = o.id
      JOIN customers c ON r.customer_id = c.id
      WHERE r.id = $1
    `;

    const itemsQuery = `
      SELECT 
        ri.*,
        oi.product_id,
        oi.unit_price,
        p.name as product_name,
        p.sku as product_sku
      FROM return_items ri
      JOIN order_items oi ON ri.order_item_id = oi.id
      JOIN products p ON oi.product_id = p.id
      WHERE ri.return_id = $1
    `;

    const [returnResult, itemsResult] = await Promise.all([
      query(returnQuery, [returnId]),
      query(itemsQuery, [returnId])
    ]);

    if (returnResult.rows.length === 0) {
      return null;
    }

    return {
      ...returnResult.rows[0],
      items: itemsResult.rows
    };
  }

  /**
   * Get returns with filtering and pagination
   */
  async getReturns(filters = {}, pagination = { page: 1, limit: 20 }) {
    const { page, limit } = pagination;
    const offset = (page - 1) * limit;

    let whereConditions = ['1=1'];
    let queryParams = [];
    let paramIndex = 1;

    // Build dynamic WHERE clause
    if (filters.status) {
      whereConditions.push(`r.status = $${paramIndex++}`);
      queryParams.push(filters.status);
    }

    if (filters.customer_id) {
      whereConditions.push(`r.customer_id = $${paramIndex++}`);
      queryParams.push(filters.customer_id);
    }

    if (filters.date_from) {
      whereConditions.push(`r.created_at >= $${paramIndex++}`);
      queryParams.push(filters.date_from);
    }

    if (filters.date_to) {
      whereConditions.push(`r.created_at <= $${paramIndex++}`);
      queryParams.push(filters.date_to);
    }

    if (filters.search) {
      whereConditions.push(`(r.return_number ILIKE $${paramIndex} OR o.order_number ILIKE $${paramIndex} OR c.email ILIKE $${paramIndex})`);
      queryParams.push(`%${filters.search}%`);
      paramIndex++;
    }

    const whereClause = whereConditions.join(' AND ');

    // Main query
    const returnsQuery = `
      SELECT 
        r.*,
        o.order_number,
        c.email as customer_email,
        c.first_name as customer_first_name,
        c.last_name as customer_last_name
      FROM returns r
      JOIN orders o ON r.order_id = o.id
      JOIN customers c ON r.customer_id = c.id
      WHERE ${whereClause}
      ORDER BY r.created_at DESC
      LIMIT $${paramIndex++} OFFSET $${paramIndex++}
    `;

    queryParams.push(limit, offset);

    // Count query
    const countQuery = `
      SELECT COUNT(*) as total
      FROM returns r
      JOIN orders o ON r.order_id = o.id
      JOIN customers c ON r.customer_id = c.id
      WHERE ${whereClause}
    `;

    const [returnsResult, countResult] = await Promise.all([
      query(returnsQuery, queryParams.slice(0, -2)),
      query(countQuery, queryParams.slice(0, -2))
    ]);

    const total = parseInt(countResult.rows[0].total);
    const totalPages = Math.ceil(total / limit);

    return {
      returns: returnsResult.rows,
      pagination: {
        page,
        limit,
        total,
        totalPages,
        hasNext: page < totalPages,
        hasPrev: page > 1
      }
    };
  }

  /**
   * Validate return request
   */
  async validateReturnRequest(returnData) {
    // Check if order exists and belongs to customer
    const orderQuery = `
      SELECT * FROM orders 
      WHERE id = $1 AND customer_id = $2 AND status = 'delivered'
    `;

    const orderResult = await query(orderQuery, [returnData.order_id, returnData.customer_id]);

    if (orderResult.rows.length === 0) {
      throw new Error('Order not found or not eligible for return');
    }

    const order = orderResult.rows[0];

    // Check return window (e.g., 30 days)
    const returnWindowDays = 30;
    const orderDate = new Date(order.created_at);
    const currentDate = new Date();
    const daysDifference = Math.floor((currentDate - orderDate) / (1000 * 60 * 60 * 24));

    if (daysDifference > returnWindowDays) {
      throw new Error(`Return window of ${returnWindowDays} days has expired`);
    }

    // Validate return items
    for (const item of returnData.items) {
      const orderItemQuery = `
        SELECT * FROM order_items 
        WHERE id = $1 AND order_id = $2
      `;

      const orderItemResult = await query(orderItemQuery, [item.order_item_id, returnData.order_id]);

      if (orderItemResult.rows.length === 0) {
        throw new Error(`Order item ${item.order_item_id} not found in order`);
      }

      const orderItem = orderItemResult.rows[0];

      if (item.quantity > orderItem.quantity) {
        throw new Error(`Return quantity cannot exceed ordered quantity for item ${item.order_item_id}`);
      }
    }

    return true;
  }

  /**
   * Calculate refund amount
   */
  async calculateRefundAmount(returnData) {
    let subtotal = 0;
    let restockingFee = 0;

    for (const item of returnData.items) {
      const orderItemQuery = `
        SELECT unit_price FROM order_items WHERE id = $1
      `;

      const result = await query(orderItemQuery, [item.order_item_id]);
      const orderItem = result.rows[0];

      const itemTotal = orderItem.unit_price * item.quantity;
      subtotal += itemTotal;

      // Apply restocking fee for certain return reasons
      if (this.shouldApplyRestockingFee(returnData.reason)) {
        restockingFee += itemTotal * 0.15; // 15% restocking fee
      }
    }

    const total = subtotal - restockingFee;

    return {
      subtotal,
      restockingFee,
      total: Math.max(0, total)
    };
  }

  /**
   * Calculate final refund amount based on received items
   */
  async calculateFinalRefundAmount(returnRequest, receivedItems) {
    let refundAmount = 0;

    for (const receivedItem of receivedItems) {
      const orderItemQuery = `
        SELECT unit_price FROM order_items WHERE id = $1
      `;

      const result = await query(orderItemQuery, [receivedItem.order_item_id]);
      const orderItem = result.rows[0];

      let itemRefund = orderItem.unit_price * receivedItem.quantity;

      // Apply condition-based adjustments
      if (receivedItem.condition === 'damaged') {
        itemRefund *= 0.5; // 50% refund for damaged items
      } else if (receivedItem.condition === 'used') {
        itemRefund *= 0.8; // 80% refund for used items
      }

      refundAmount += itemRefund;
    }

    // Subtract restocking fee if applicable
    refundAmount -= returnRequest.restocking_fee || 0;

    return Math.max(0, refundAmount);
  }

  /**
   * Generate return number
   */
  async generateReturnNumber() {
    const prefix = 'RET';
    const timestamp = Date.now().toString().slice(-8);
    const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0');
    
    return `${prefix}-${timestamp}-${random}`;
  }

  /**
   * Generate return shipping label
   */
  async generateReturnShippingLabel(returnRequest) {
    // In a real implementation, this would integrate with shipping carriers
    // to generate actual return shipping labels
    
    return {
      label_url: `https://returns.orderflowpro.com/labels/${returnRequest.return_number}`,
      tracking_number: `RET${Date.now()}`,
      carrier: 'UPS',
      service: 'UPS Ground'
    };
  }

  /**
   * Process refund
   */
  async processRefund(returnRequest, refundAmount) {
    // In a real implementation, this would integrate with payment processors
    // to process actual refunds
    
    logger.info('Refund processed', {
      return_id: returnRequest.id,
      return_number: returnRequest.return_number,
      customer_id: returnRequest.customer_id,
      refund_amount: refundAmount
    });

    // Here you would call payment processor APIs to issue refund
    // For example: stripe.refunds.create(), paypal.refund(), etc.
    
    return {
      refund_id: `ref_${Date.now()}`,
      amount: refundAmount,
      status: 'processed',
      processed_at: new Date()
    };
  }

  /**
   * Check if restocking fee should be applied
   */
  shouldApplyRestockingFee(reason) {
    const feeReasons = [
      this.returnReasons.CHANGED_MIND,
      this.returnReasons.SIZE_ISSUE
    ];

    return feeReasons.includes(reason);
  }

  /**
   * Check if item can be restocked
   */
  canRestockItem(condition) {
    const restockableConditions = ['new', 'like_new'];
    return restockableConditions.includes(condition);
  }

  /**
   * Get return statistics
   */
  async getReturnStatistics(dateFrom, dateTo) {
    const statsQuery = `
      SELECT 
        COUNT(*) as total_returns,
        COUNT(CASE WHEN status = 'approved' THEN 1 END) as approved_returns,
        COUNT(CASE WHEN status = 'rejected' THEN 1 END) as rejected_returns,
        COUNT(CASE WHEN status = 'processed' THEN 1 END) as processed_returns,
        AVG(refund_amount) as avg_refund_amount,
        SUM(refund_amount) as total_refund_amount,
        AVG(EXTRACT(EPOCH FROM (updated_at - created_at))/3600) as avg_processing_hours
      FROM returns
      WHERE created_at >= $1 AND created_at <= $2
    `;

    const result = await query(statsQuery, [dateFrom, dateTo]);
    return result.rows[0];
  }

  /**
   * Get return reasons analysis
   */
  async getReturnReasonsAnalysis(dateFrom, dateTo) {
    const reasonsQuery = `
      SELECT 
        reason,
        COUNT(*) as count,
        AVG(refund_amount) as avg_refund_amount
      FROM returns
      WHERE created_at >= $1 AND created_at <= $2
      GROUP BY reason
      ORDER BY count DESC
    `;

    const result = await query(reasonsQuery, [dateFrom, dateTo]);
    return result.rows;
  }
}

module.exports = new ReturnsService();
