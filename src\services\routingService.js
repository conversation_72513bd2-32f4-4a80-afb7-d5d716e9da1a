const { query } = require('../config/database');
const inventoryService = require('./inventoryService');
const logger = require('../utils/logger');

class RoutingService {
  /**
   * Select optimal fulfillment center for an order
   */
  async selectOptimalFulfillmentCenter(orderItems, shippingAddress) {
    try {
      // Get all active fulfillment centers
      const fulfillmentCenters = await this.getActiveFulfillmentCenters();
      
      if (fulfillmentCenters.length === 0) {
        throw new Error('No active fulfillment centers available');
      }

      // Score each fulfillment center
      const scoredCenters = await Promise.all(
        fulfillmentCenters.map(fc => this.scoreFulfillmentCenter(fc, orderItems, shippingAddress))
      );

      // Filter out centers that can't fulfill the order
      const viableCenters = scoredCenters.filter(scored => scored.canFulfill);

      if (viableCenters.length === 0) {
        throw new Error('No fulfillment center can fulfill this order');
      }

      // Sort by score (higher is better)
      viableCenters.sort((a, b) => b.totalScore - a.totalScore);

      const selectedCenter = viableCenters[0];

      logger.info('Fulfillment center selected', {
        selected_center: selectedCenter.fulfillmentCenter.name,
        score: selectedCenter.totalScore,
        alternatives: viableCenters.slice(1, 3).map(fc => ({
          name: fc.fulfillmentCenter.name,
          score: fc.totalScore
        }))
      });

      return selectedCenter.fulfillmentCenter;

    } catch (error) {
      logger.error('Error selecting fulfillment center:', error);
      throw error;
    }
  }

  /**
   * Score a fulfillment center for an order
   */
  async scoreFulfillmentCenter(fulfillmentCenter, orderItems, shippingAddress) {
    const scoring = {
      fulfillmentCenter,
      canFulfill: true,
      inventoryScore: 0,
      distanceScore: 0,
      capacityScore: 0,
      costScore: 0,
      totalScore: 0,
      details: {}
    };

    try {
      // Check inventory availability
      const inventoryCheck = await this.checkInventoryAvailability(fulfillmentCenter.id, orderItems);
      scoring.canFulfill = inventoryCheck.canFulfill;
      scoring.inventoryScore = inventoryCheck.score;
      scoring.details.inventory = inventoryCheck.details;

      if (!scoring.canFulfill) {
        return scoring;
      }

      // Calculate distance score
      const distanceInfo = await this.calculateDistanceScore(fulfillmentCenter, shippingAddress);
      scoring.distanceScore = distanceInfo.score;
      scoring.details.distance = distanceInfo;

      // Calculate capacity score
      const capacityInfo = await this.calculateCapacityScore(fulfillmentCenter);
      scoring.capacityScore = capacityInfo.score;
      scoring.details.capacity = capacityInfo;

      // Calculate cost score
      const costInfo = await this.calculateCostScore(fulfillmentCenter, orderItems, shippingAddress);
      scoring.costScore = costInfo.score;
      scoring.details.cost = costInfo;

      // Calculate weighted total score
      const weights = {
        inventory: 0.4,
        distance: 0.3,
        capacity: 0.2,
        cost: 0.1
      };

      scoring.totalScore = 
        (scoring.inventoryScore * weights.inventory) +
        (scoring.distanceScore * weights.distance) +
        (scoring.capacityScore * weights.capacity) +
        (scoring.costScore * weights.cost);

    } catch (error) {
      logger.error('Error scoring fulfillment center:', {
        fulfillment_center: fulfillmentCenter.name,
        error: error.message
      });
      scoring.canFulfill = false;
    }

    return scoring;
  }

  /**
   * Check inventory availability at fulfillment center
   */
  async checkInventoryAvailability(fulfillmentCenterId, orderItems) {
    const result = {
      canFulfill: true,
      score: 0,
      details: {
        items: [],
        totalAvailable: 0,
        totalRequested: 0
      }
    };

    let totalAvailabilityRatio = 0;

    for (const item of orderItems) {
      const availability = await inventoryService.checkAvailability(
        item.product_id,
        item.quantity,
        fulfillmentCenterId
      );

      const itemDetail = {
        product_id: item.product_id,
        requested: item.quantity,
        available: availability.quantity,
        canFulfill: availability.available
      };

      result.details.items.push(itemDetail);
      result.details.totalRequested += item.quantity;
      result.details.totalAvailable += availability.quantity;

      if (!availability.available) {
        result.canFulfill = false;
      }

      // Calculate availability ratio for scoring
      const ratio = Math.min(availability.quantity / item.quantity, 1);
      totalAvailabilityRatio += ratio;
    }

    // Score based on average availability ratio (0-100)
    result.score = (totalAvailabilityRatio / orderItems.length) * 100;

    return result;
  }

  /**
   * Calculate distance-based score
   */
  async calculateDistanceScore(fulfillmentCenter, shippingAddress) {
    try {
      // Parse fulfillment center coordinates
      const fcCoordinates = this.parseCoordinates(fulfillmentCenter.coordinates);
      
      // Get shipping address coordinates (you might want to use a geocoding service)
      const shippingCoordinates = await this.getAddressCoordinates(shippingAddress);

      // Calculate distance
      const distance = this.calculateDistance(fcCoordinates, shippingCoordinates);

      // Score inversely proportional to distance (closer = higher score)
      // Max distance considered: 3000 miles
      const maxDistance = 3000;
      const score = Math.max(0, (maxDistance - distance) / maxDistance * 100);

      return {
        score,
        distance,
        unit: 'miles'
      };

    } catch (error) {
      logger.warn('Error calculating distance score:', error);
      // Return neutral score if distance calculation fails
      return {
        score: 50,
        distance: null,
        error: error.message
      };
    }
  }

  /**
   * Calculate capacity-based score
   */
  async calculateCapacityScore(fulfillmentCenter) {
    try {
      // Get current utilization of fulfillment center
      const utilizationQuery = `
        SELECT 
          fc.capacity,
          COALESCE(SUM(i.quantity_available + i.quantity_reserved), 0) as current_inventory
        FROM fulfillment_centers fc
        LEFT JOIN inventory i ON fc.id = i.fulfillment_center_id
        WHERE fc.id = $1
        GROUP BY fc.id, fc.capacity
      `;

      const result = await query(utilizationQuery, [fulfillmentCenter.id]);
      
      if (result.rows.length === 0) {
        return { score: 50, utilization: null };
      }

      const { capacity, current_inventory } = result.rows[0];
      const utilization = current_inventory / capacity;

      // Score inversely proportional to utilization (less utilized = higher score)
      const score = Math.max(0, (1 - utilization) * 100);

      return {
        score,
        utilization: utilization * 100,
        capacity,
        current_inventory
      };

    } catch (error) {
      logger.warn('Error calculating capacity score:', error);
      return { score: 50, error: error.message };
    }
  }

  /**
   * Calculate cost-based score
   */
  async calculateCostScore(fulfillmentCenter, orderItems, shippingAddress) {
    try {
      // This is a simplified cost calculation
      // In a real implementation, you'd integrate with shipping carriers' APIs
      
      const baseCost = 5.00; // Base fulfillment cost
      const distanceInfo = await this.calculateDistanceScore(fulfillmentCenter, shippingAddress);
      
      // Estimate shipping cost based on distance
      const shippingCost = Math.max(5.00, distanceInfo.distance * 0.01);
      
      // Calculate handling cost based on number of items
      const handlingCost = orderItems.length * 1.50;
      
      const totalCost = baseCost + shippingCost + handlingCost;

      // Score inversely proportional to cost (lower cost = higher score)
      // Assume max reasonable cost is $50
      const maxCost = 50;
      const score = Math.max(0, (maxCost - totalCost) / maxCost * 100);

      return {
        score,
        totalCost,
        breakdown: {
          baseCost,
          shippingCost,
          handlingCost
        }
      };

    } catch (error) {
      logger.warn('Error calculating cost score:', error);
      return { score: 50, error: error.message };
    }
  }

  /**
   * Get active fulfillment centers
   */
  async getActiveFulfillmentCenters() {
    const centersQuery = `
      SELECT * FROM fulfillment_centers 
      WHERE is_active = true 
      ORDER BY name
    `;

    const result = await query(centersQuery);
    return result.rows;
  }

  /**
   * Parse coordinates from PostgreSQL POINT type
   */
  parseCoordinates(coordinatesPoint) {
    if (!coordinatesPoint) {
      throw new Error('No coordinates available');
    }

    // PostgreSQL POINT format: (longitude,latitude)
    const match = coordinatesPoint.match(/\(([^,]+),([^)]+)\)/);
    if (!match) {
      throw new Error('Invalid coordinates format');
    }

    return {
      longitude: parseFloat(match[1]),
      latitude: parseFloat(match[2])
    };
  }

  /**
   * Get coordinates for an address (simplified - in production use geocoding service)
   */
  async getAddressCoordinates(address) {
    // This is a simplified implementation
    // In production, you'd use a geocoding service like Google Maps API
    
    const cityCoordinates = {
      'new york': { latitude: 40.7128, longitude: -74.0060 },
      'los angeles': { latitude: 34.0522, longitude: -118.2437 },
      'chicago': { latitude: 41.8781, longitude: -87.6298 },
      'houston': { latitude: 29.7604, longitude: -95.3698 },
      'phoenix': { latitude: 33.4484, longitude: -112.0740 },
      'philadelphia': { latitude: 39.9526, longitude: -75.1652 },
      'san antonio': { latitude: 29.4241, longitude: -98.4936 },
      'san diego': { latitude: 32.7157, longitude: -117.1611 },
      'dallas': { latitude: 32.7767, longitude: -96.7970 },
      'san jose': { latitude: 37.3382, longitude: -121.8863 }
    };

    const city = address.city?.toLowerCase();
    
    if (cityCoordinates[city]) {
      return cityCoordinates[city];
    }

    // Default to center of US if city not found
    return { latitude: 39.8283, longitude: -98.5795 };
  }

  /**
   * Calculate distance between two coordinates using Haversine formula
   */
  calculateDistance(coord1, coord2) {
    const R = 3959; // Earth's radius in miles
    
    const lat1Rad = coord1.latitude * Math.PI / 180;
    const lat2Rad = coord2.latitude * Math.PI / 180;
    const deltaLatRad = (coord2.latitude - coord1.latitude) * Math.PI / 180;
    const deltaLonRad = (coord2.longitude - coord1.longitude) * Math.PI / 180;

    const a = Math.sin(deltaLatRad / 2) * Math.sin(deltaLatRad / 2) +
              Math.cos(lat1Rad) * Math.cos(lat2Rad) *
              Math.sin(deltaLonRad / 2) * Math.sin(deltaLonRad / 2);
    
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
    
    return R * c; // Distance in miles
  }

  /**
   * Get routing recommendations for multiple orders
   */
  async getRoutingRecommendations(orders) {
    const recommendations = [];

    for (const order of orders) {
      try {
        const recommendation = await this.selectOptimalFulfillmentCenter(
          order.items,
          order.shipping_address
        );

        recommendations.push({
          order_id: order.id,
          recommended_fulfillment_center: recommendation,
          success: true
        });

      } catch (error) {
        recommendations.push({
          order_id: order.id,
          success: false,
          error: error.message
        });
      }
    }

    return recommendations;
  }

  /**
   * Analyze fulfillment center performance
   */
  async analyzeFulfillmentCenterPerformance(fulfillmentCenterId, days = 30) {
    const performanceQuery = `
      SELECT 
        COUNT(o.id) as total_orders,
        AVG(EXTRACT(EPOCH FROM (o.updated_at - o.created_at))/3600) as avg_processing_hours,
        COUNT(CASE WHEN o.status = 'delivered' THEN 1 END) as delivered_orders,
        COUNT(CASE WHEN o.status = 'cancelled' THEN 1 END) as cancelled_orders,
        AVG(o.total_amount) as avg_order_value
      FROM orders o
      WHERE o.fulfillment_center_id = $1
        AND o.created_at >= NOW() - INTERVAL '${days} days'
    `;

    const result = await query(performanceQuery, [fulfillmentCenterId]);
    
    if (result.rows.length === 0) {
      return null;
    }

    const performance = result.rows[0];
    
    // Calculate performance metrics
    const deliveryRate = performance.total_orders > 0 ? 
      (performance.delivered_orders / performance.total_orders) * 100 : 0;
    
    const cancellationRate = performance.total_orders > 0 ? 
      (performance.cancelled_orders / performance.total_orders) * 100 : 0;

    return {
      fulfillment_center_id: fulfillmentCenterId,
      period_days: days,
      total_orders: parseInt(performance.total_orders),
      avg_processing_hours: parseFloat(performance.avg_processing_hours || 0),
      delivery_rate: deliveryRate,
      cancellation_rate: cancellationRate,
      avg_order_value: parseFloat(performance.avg_order_value || 0)
    };
  }

  /**
   * Get optimal routing strategy for a batch of orders
   */
  async getOptimalRoutingStrategy(orders) {
    const strategy = {
      total_orders: orders.length,
      fulfillment_centers: {},
      recommendations: [],
      estimated_savings: 0
    };

    for (const order of orders) {
      const recommendation = await this.selectOptimalFulfillmentCenter(
        order.items,
        order.shipping_address
      );

      const fcId = recommendation.id;
      
      if (!strategy.fulfillment_centers[fcId]) {
        strategy.fulfillment_centers[fcId] = {
          name: recommendation.name,
          order_count: 0,
          total_value: 0
        };
      }

      strategy.fulfillment_centers[fcId].order_count++;
      strategy.fulfillment_centers[fcId].total_value += order.total_amount || 0;

      strategy.recommendations.push({
        order_id: order.id,
        fulfillment_center_id: fcId,
        fulfillment_center_name: recommendation.name
      });
    }

    return strategy;
  }
}

module.exports = new RoutingService();
