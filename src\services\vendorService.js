const { query, transaction } = require('../config/database');
const inventoryService = require('./inventoryService');
const notificationService = require('./notificationService');
const logger = require('../utils/logger');
const { v4: uuidv4 } = require('uuid');

class VendorService {
  constructor() {
    this.poStatuses = {
      PENDING: 'pending',
      SENT: 'sent',
      ACKNOWLEDGED: 'acknowledged',
      SHIPPED: 'shipped',
      RECEIVED: 'received',
      CANCELLED: 'cancelled'
    };
  }

  /**
   * Create purchase order
   */
  async createPurchaseOrder(poData) {
    return await transaction(async (client) => {
      const poId = uuidv4();
      const poNumber = await this.generatePONumber();

      // Calculate total amount
      const totalAmount = poData.items.reduce((sum, item) => 
        sum + (item.quantity * item.unit_cost), 0
      );

      // Create purchase order
      const poQuery = `
        INSERT INTO purchase_orders (
          id, po_number, vendor_id, status, total_amount,
          expected_delivery_date, notes, created_at, updated_at
        ) VALUES ($1, $2, $3, $4, $5, $6, $7, NOW(), NOW())
        RETURNING *
      `;

      const poValues = [
        poId,
        poNumber,
        poData.vendor_id,
        this.poStatuses.PENDING,
        totalAmount,
        poData.expected_delivery_date,
        poData.notes
      ];

      const poResult = await client.query(poQuery, poValues);

      // Create purchase order items
      for (const item of poData.items) {
        const poItemQuery = `
          INSERT INTO purchase_order_items (
            id, purchase_order_id, product_id, quantity, unit_cost, total_cost, created_at
          ) VALUES ($1, $2, $3, $4, $5, $6, NOW())
        `;

        const poItemValues = [
          uuidv4(),
          poId,
          item.product_id,
          item.quantity,
          item.unit_cost,
          item.quantity * item.unit_cost
        ];

        await client.query(poItemQuery, poItemValues);
      }

      const purchaseOrder = poResult.rows[0];

      // Send PO to vendor
      await this.sendPurchaseOrderToVendor(poId);

      logger.info('Purchase order created', {
        po_id: poId,
        po_number: poNumber,
        vendor_id: poData.vendor_id,
        total_amount: totalAmount,
        items_count: poData.items.length
      });

      return await this.getPurchaseOrderById(poId);
    });
  }

  /**
   * Auto-generate purchase orders for low stock items
   */
  async autoGeneratePurchaseOrders() {
    try {
      // Get low stock items
      const lowStockItems = await inventoryService.getLowStockItems();
      
      if (lowStockItems.length === 0) {
        logger.info('No low stock items found for auto PO generation');
        return [];
      }

      // Group items by vendor
      const itemsByVendor = await this.groupItemsByVendor(lowStockItems);
      
      const generatedPOs = [];

      for (const [vendorId, items] of Object.entries(itemsByVendor)) {
        try {
          const po = await this.createAutoPurchaseOrder(vendorId, items);
          generatedPOs.push(po);
        } catch (error) {
          logger.error('Failed to create auto PO for vendor:', {
            vendor_id: vendorId,
            error: error.message
          });
        }
      }

      logger.info('Auto purchase orders generated', {
        low_stock_items: lowStockItems.length,
        pos_generated: generatedPOs.length
      });

      return generatedPOs;

    } catch (error) {
      logger.error('Error in auto PO generation:', error);
      throw error;
    }
  }

  /**
   * Create automatic purchase order for vendor
   */
  async createAutoPurchaseOrder(vendorId, lowStockItems) {
    // Get vendor details
    const vendor = await this.getVendorById(vendorId);
    if (!vendor) {
      throw new Error(`Vendor ${vendorId} not found`);
    }

    // Calculate order quantities
    const poItems = [];
    for (const item of lowStockItems) {
      const orderQuantity = await this.calculateOptimalOrderQuantity(item);
      const vendorProduct = await this.getVendorProduct(vendorId, item.product_id);
      
      if (vendorProduct && orderQuantity > 0) {
        poItems.push({
          product_id: item.product_id,
          quantity: orderQuantity,
          unit_cost: vendorProduct.cost
        });
      }
    }

    if (poItems.length === 0) {
      throw new Error('No items to order from vendor');
    }

    // Calculate expected delivery date
    const expectedDeliveryDate = new Date();
    expectedDeliveryDate.setDate(expectedDeliveryDate.getDate() + vendor.lead_time_days);

    const poData = {
      vendor_id: vendorId,
      items: poItems,
      expected_delivery_date: expectedDeliveryDate,
      notes: 'Auto-generated purchase order for low stock items'
    };

    return await this.createPurchaseOrder(poData);
  }

  /**
   * Receive purchase order shipment
   */
  async receivePurchaseOrder(poId, receivedItems, receivedBy) {
    return await transaction(async (client) => {
      const po = await this.getPurchaseOrderById(poId);
      
      if (!po) {
        throw new Error('Purchase order not found');
      }

      if (po.status !== this.poStatuses.SHIPPED) {
        throw new Error('Purchase order must be shipped before receiving');
      }

      // Update PO status
      await client.query(
        'UPDATE purchase_orders SET status = $1, updated_at = NOW() WHERE id = $2',
        [this.poStatuses.RECEIVED, poId]
      );

      // Process received items
      for (const receivedItem of receivedItems) {
        await this.processReceivedItem(client, po, receivedItem);
      }

      // Send receipt confirmation
      await this.sendReceiptConfirmation(po, receivedItems, receivedBy);

      logger.info('Purchase order received', {
        po_id: poId,
        po_number: po.po_number,
        received_items: receivedItems.length,
        received_by: receivedBy
      });

      return await this.getPurchaseOrderById(poId);
    });
  }

  /**
   * Process received item
   */
  async processReceivedItem(client, po, receivedItem) {
    // Get PO item details
    const poItemQuery = `
      SELECT poi.*, p.name as product_name, p.sku as product_sku
      FROM purchase_order_items poi
      JOIN products p ON poi.product_id = p.id
      WHERE poi.id = $1
    `;

    const poItemResult = await client.query(poItemQuery, [receivedItem.po_item_id]);
    const poItem = poItemResult.rows[0];

    if (!poItem) {
      throw new Error(`PO item ${receivedItem.po_item_id} not found`);
    }

    // Determine fulfillment center (use default or specified)
    const fulfillmentCenterId = receivedItem.fulfillment_center_id || 
      await this.getDefaultFulfillmentCenter();

    // Add to inventory
    await inventoryService.updateInventory(
      poItem.product_id,
      fulfillmentCenterId,
      receivedItem.quantity_received,
      'purchase_order',
      po.id
    );

    logger.info('Inventory updated from PO receipt', {
      po_id: po.id,
      product_id: poItem.product_id,
      product_sku: poItem.product_sku,
      quantity_received: receivedItem.quantity_received,
      fulfillment_center_id: fulfillmentCenterId
    });
  }

  /**
   * Get purchase order by ID
   */
  async getPurchaseOrderById(poId) {
    const poQuery = `
      SELECT 
        po.*,
        v.name as vendor_name,
        v.contact_email as vendor_email,
        v.contact_phone as vendor_phone
      FROM purchase_orders po
      JOIN vendors v ON po.vendor_id = v.id
      WHERE po.id = $1
    `;

    const itemsQuery = `
      SELECT 
        poi.*,
        p.name as product_name,
        p.sku as product_sku
      FROM purchase_order_items poi
      JOIN products p ON poi.product_id = p.id
      WHERE poi.purchase_order_id = $1
    `;

    const [poResult, itemsResult] = await Promise.all([
      query(poQuery, [poId]),
      query(itemsQuery, [poId])
    ]);

    if (poResult.rows.length === 0) {
      return null;
    }

    return {
      ...poResult.rows[0],
      items: itemsResult.rows
    };
  }

  /**
   * Get vendor by ID
   */
  async getVendorById(vendorId) {
    const vendorQuery = `
      SELECT * FROM vendors WHERE id = $1 AND is_active = true
    `;

    const result = await query(vendorQuery, [vendorId]);
    return result.rows[0] || null;
  }

  /**
   * Get vendor product relationship
   */
  async getVendorProduct(vendorId, productId) {
    const vpQuery = `
      SELECT * FROM vendor_products 
      WHERE vendor_id = $1 AND product_id = $2
    `;

    const result = await query(vpQuery, [vendorId, productId]);
    return result.rows[0] || null;
  }

  /**
   * Group low stock items by preferred vendor
   */
  async groupItemsByVendor(lowStockItems) {
    const itemsByVendor = {};

    for (const item of lowStockItems) {
      // Get preferred vendor for this product
      const vendorQuery = `
        SELECT vendor_id FROM vendor_products 
        WHERE product_id = $1 AND is_preferred = true
        ORDER BY cost ASC
        LIMIT 1
      `;

      const result = await query(vendorQuery, [item.product_id]);
      
      if (result.rows.length > 0) {
        const vendorId = result.rows[0].vendor_id;
        
        if (!itemsByVendor[vendorId]) {
          itemsByVendor[vendorId] = [];
        }
        
        itemsByVendor[vendorId].push(item);
      }
    }

    return itemsByVendor;
  }

  /**
   * Calculate optimal order quantity
   */
  async calculateOptimalOrderQuantity(item) {
    // Simple EOQ-like calculation
    const currentStock = item.quantity_available;
    const reorderPoint = item.reorder_point;
    const maxStockLevel = item.max_stock_level;

    // Order enough to reach max stock level
    const optimalQuantity = maxStockLevel - currentStock;

    // Get vendor minimum order quantity
    const vendorProductQuery = `
      SELECT minimum_order_quantity FROM vendor_products 
      WHERE product_id = $1 AND is_preferred = true
      LIMIT 1
    `;

    const result = await query(vendorProductQuery, [item.product_id]);
    const minOrderQty = result.rows[0]?.minimum_order_quantity || 1;

    return Math.max(optimalQuantity, minOrderQty);
  }

  /**
   * Send purchase order to vendor
   */
  async sendPurchaseOrderToVendor(poId) {
    const po = await this.getPurchaseOrderById(poId);
    
    if (!po) {
      throw new Error('Purchase order not found');
    }

    // Generate PO document
    const poDocument = await this.generatePODocument(po);

    // Send email to vendor
    const emailSent = await notificationService.sendPurchaseOrderToVendor(po, poDocument);

    // Update PO status
    if (emailSent) {
      await query(
        'UPDATE purchase_orders SET status = $1, updated_at = NOW() WHERE id = $2',
        [this.poStatuses.SENT, poId]
      );
    }

    logger.info('Purchase order sent to vendor', {
      po_id: poId,
      po_number: po.po_number,
      vendor_email: po.vendor_email,
      email_sent: emailSent
    });

    return emailSent;
  }

  /**
   * Generate PO document
   */
  async generatePODocument(po) {
    // In a real implementation, this would generate a PDF document
    // For now, return a simple object
    
    return {
      po_number: po.po_number,
      vendor_name: po.vendor_name,
      total_amount: po.total_amount,
      expected_delivery_date: po.expected_delivery_date,
      items: po.items,
      created_at: po.created_at
    };
  }

  /**
   * Send receipt confirmation
   */
  async sendReceiptConfirmation(po, receivedItems, receivedBy) {
    // Send confirmation to vendor and internal team
    await notificationService.sendPOReceiptConfirmation(po, receivedItems, receivedBy);
  }

  /**
   * Generate PO number
   */
  async generatePONumber() {
    const prefix = 'PO';
    const timestamp = Date.now().toString().slice(-8);
    const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0');
    
    return `${prefix}-${timestamp}-${random}`;
  }

  /**
   * Get default fulfillment center
   */
  async getDefaultFulfillmentCenter() {
    const fcQuery = `
      SELECT id FROM fulfillment_centers 
      WHERE is_active = true 
      ORDER BY name 
      LIMIT 1
    `;

    const result = await query(fcQuery);
    return result.rows[0]?.id;
  }

  /**
   * Get purchase orders with filtering
   */
  async getPurchaseOrders(filters = {}, pagination = { page: 1, limit: 20 }) {
    const { page, limit } = pagination;
    const offset = (page - 1) * limit;

    let whereConditions = ['1=1'];
    let queryParams = [];
    let paramIndex = 1;

    if (filters.status) {
      whereConditions.push(`po.status = $${paramIndex++}`);
      queryParams.push(filters.status);
    }

    if (filters.vendor_id) {
      whereConditions.push(`po.vendor_id = $${paramIndex++}`);
      queryParams.push(filters.vendor_id);
    }

    if (filters.date_from) {
      whereConditions.push(`po.created_at >= $${paramIndex++}`);
      queryParams.push(filters.date_from);
    }

    if (filters.date_to) {
      whereConditions.push(`po.created_at <= $${paramIndex++}`);
      queryParams.push(filters.date_to);
    }

    const whereClause = whereConditions.join(' AND ');

    const posQuery = `
      SELECT 
        po.*,
        v.name as vendor_name
      FROM purchase_orders po
      JOIN vendors v ON po.vendor_id = v.id
      WHERE ${whereClause}
      ORDER BY po.created_at DESC
      LIMIT $${paramIndex++} OFFSET $${paramIndex++}
    `;

    queryParams.push(limit, offset);

    const countQuery = `
      SELECT COUNT(*) as total
      FROM purchase_orders po
      WHERE ${whereClause}
    `;

    const [posResult, countResult] = await Promise.all([
      query(posQuery, queryParams.slice(0, -2)),
      query(countQuery, queryParams.slice(0, -2))
    ]);

    const total = parseInt(countResult.rows[0].total);
    const totalPages = Math.ceil(total / limit);

    return {
      purchase_orders: posResult.rows,
      pagination: {
        page,
        limit,
        total,
        totalPages,
        hasNext: page < totalPages,
        hasPrev: page > 1
      }
    };
  }

  /**
   * Get vendor performance metrics
   */
  async getVendorPerformance(vendorId, days = 90) {
    const performanceQuery = `
      SELECT 
        COUNT(po.id) as total_pos,
        AVG(po.total_amount) as avg_po_amount,
        SUM(po.total_amount) as total_po_amount,
        COUNT(CASE WHEN po.status = 'received' THEN 1 END) as completed_pos,
        AVG(EXTRACT(EPOCH FROM (po.updated_at - po.created_at))/86400) as avg_delivery_days
      FROM purchase_orders po
      WHERE po.vendor_id = $1
        AND po.created_at >= NOW() - INTERVAL '${days} days'
    `;

    const result = await query(performanceQuery, [vendorId]);
    const performance = result.rows[0];

    const completionRate = performance.total_pos > 0 ? 
      (performance.completed_pos / performance.total_pos) * 100 : 0;

    return {
      vendor_id: vendorId,
      period_days: days,
      total_purchase_orders: parseInt(performance.total_pos),
      completed_purchase_orders: parseInt(performance.completed_pos),
      completion_rate: completionRate,
      avg_po_amount: parseFloat(performance.avg_po_amount || 0),
      total_po_amount: parseFloat(performance.total_po_amount || 0),
      avg_delivery_days: parseFloat(performance.avg_delivery_days || 0)
    };
  }

  /**
   * Get vendor list with performance summary
   */
  async getVendorsWithPerformance() {
    const vendorsQuery = `
      SELECT 
        v.*,
        COUNT(po.id) as total_pos_last_90_days,
        AVG(po.total_amount) as avg_po_amount,
        COUNT(CASE WHEN po.status = 'received' THEN 1 END) as completed_pos
      FROM vendors v
      LEFT JOIN purchase_orders po ON v.id = po.vendor_id 
        AND po.created_at >= NOW() - INTERVAL '90 days'
      WHERE v.is_active = true
      GROUP BY v.id
      ORDER BY v.name
    `;

    const result = await query(vendorsQuery);
    
    return result.rows.map(vendor => ({
      ...vendor,
      completion_rate: vendor.total_pos_last_90_days > 0 ? 
        (vendor.completed_pos / vendor.total_pos_last_90_days) * 100 : 0
    }));
  }

  /**
   * Schedule automatic PO generation
   */
  scheduleAutoPOGeneration() {
    const cron = require('node-cron');
    
    // Run every day at 9 AM
    cron.schedule('0 9 * * *', async () => {
      try {
        logger.info('Starting scheduled auto PO generation');
        await this.autoGeneratePurchaseOrders();
        logger.info('Scheduled auto PO generation completed');
      } catch (error) {
        logger.error('Scheduled auto PO generation failed:', error);
      }
    });

    logger.info('Auto PO generation scheduled for daily at 9 AM');
  }
}

module.exports = new VendorService();
