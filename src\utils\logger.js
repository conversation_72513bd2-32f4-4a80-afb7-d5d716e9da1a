const winston = require('winston');
const path = require('path');
const fs = require('fs');

// Ensure logs directory exists
const logsDir = path.join(process.cwd(), 'logs');
if (!fs.existsSync(logsDir)) {
  fs.mkdirSync(logsDir, { recursive: true });
}

// Custom format for console output
const consoleFormat = winston.format.combine(
  winston.format.colorize(),
  winston.format.timestamp({ format: 'YYYY-MM-DD HH:mm:ss' }),
  winston.format.errors({ stack: true }),
  winston.format.printf(({ timestamp, level, message, stack, ...meta }) => {
    let log = `${timestamp} [${level}]: ${message}`;
    
    if (stack) {
      log += `\n${stack}`;
    }
    
    if (Object.keys(meta).length > 0) {
      log += `\n${JSON.stringify(meta, null, 2)}`;
    }
    
    return log;
  })
);

// Custom format for file output
const fileFormat = winston.format.combine(
  winston.format.timestamp({ format: 'YYYY-MM-DD HH:mm:ss' }),
  winston.format.errors({ stack: true }),
  winston.format.json()
);

// Create logger instance
const logger = winston.createLogger({
  level: process.env.LOG_LEVEL || 'info',
  format: fileFormat,
  defaultMeta: { 
    service: 'orderflow-pro',
    environment: process.env.NODE_ENV || 'development'
  },
  transports: [
    // File transport for errors
    new winston.transports.File({
      filename: path.join(logsDir, 'error.log'),
      level: 'error',
      maxsize: 20 * 1024 * 1024, // 20MB
      maxFiles: 5,
      tailable: true
    }),
    
    // File transport for all logs
    new winston.transports.File({
      filename: path.join(logsDir, 'combined.log'),
      maxsize: 20 * 1024 * 1024, // 20MB
      maxFiles: 5,
      tailable: true
    })
  ],
  
  // Handle uncaught exceptions
  exceptionHandlers: [
    new winston.transports.File({
      filename: path.join(logsDir, 'exceptions.log'),
      maxsize: 20 * 1024 * 1024,
      maxFiles: 5
    })
  ],
  
  // Handle unhandled promise rejections
  rejectionHandlers: [
    new winston.transports.File({
      filename: path.join(logsDir, 'rejections.log'),
      maxsize: 20 * 1024 * 1024,
      maxFiles: 5
    })
  ]
});

// Add console transport for non-production environments
if (process.env.NODE_ENV !== 'production') {
  logger.add(new winston.transports.Console({
    format: consoleFormat,
    level: 'debug'
  }));
}

// Add console transport for production with limited output
if (process.env.NODE_ENV === 'production') {
  logger.add(new winston.transports.Console({
    format: consoleFormat,
    level: 'warn'
  }));
}

// Helper functions for structured logging
const logHelpers = {
  // Log HTTP requests
  logRequest: (req, res, responseTime) => {
    logger.info('HTTP Request', {
      method: req.method,
      url: req.originalUrl,
      statusCode: res.statusCode,
      responseTime: `${responseTime}ms`,
      userAgent: req.get('User-Agent'),
      ip: req.ip,
      userId: req.user?.id
    });
  },
  
  // Log database operations
  logDatabase: (operation, table, duration, error = null) => {
    const logData = {
      operation,
      table,
      duration: `${duration}ms`
    };
    
    if (error) {
      logger.error('Database Operation Failed', { ...logData, error: error.message });
    } else {
      logger.debug('Database Operation', logData);
    }
  },
  
  // Log external API calls
  logExternalApi: (service, endpoint, method, statusCode, duration, error = null) => {
    const logData = {
      service,
      endpoint,
      method,
      statusCode,
      duration: `${duration}ms`
    };
    
    if (error) {
      logger.error('External API Call Failed', { ...logData, error: error.message });
    } else {
      logger.info('External API Call', logData);
    }
  },
  
  // Log business operations
  logBusinessOperation: (operation, entityType, entityId, userId, details = {}) => {
    logger.info('Business Operation', {
      operation,
      entityType,
      entityId,
      userId,
      ...details
    });
  },
  
  // Log security events
  logSecurity: (event, userId, ip, details = {}) => {
    logger.warn('Security Event', {
      event,
      userId,
      ip,
      timestamp: new Date().toISOString(),
      ...details
    });
  },
  
  // Log performance metrics
  logPerformance: (operation, duration, details = {}) => {
    const level = duration > 5000 ? 'warn' : duration > 1000 ? 'info' : 'debug';
    
    logger.log(level, 'Performance Metric', {
      operation,
      duration: `${duration}ms`,
      ...details
    });
  },
  
  // Log cache operations
  logCache: (operation, key, hit = null, ttl = null) => {
    logger.debug('Cache Operation', {
      operation,
      key,
      hit,
      ttl: ttl ? `${ttl}s` : null
    });
  },
  
  // Log order processing events
  logOrder: (orderId, event, details = {}) => {
    logger.info('Order Event', {
      orderId,
      event,
      timestamp: new Date().toISOString(),
      ...details
    });
  },
  
  // Log inventory changes
  logInventory: (productId, fulfillmentCenterId, change, reason, details = {}) => {
    logger.info('Inventory Change', {
      productId,
      fulfillmentCenterId,
      change,
      reason,
      timestamp: new Date().toISOString(),
      ...details
    });
  },
  
  // Log notification events
  logNotification: (type, recipient, status, details = {}) => {
    logger.info('Notification Event', {
      type,
      recipient,
      status,
      timestamp: new Date().toISOString(),
      ...details
    });
  }
};

// Extend logger with helper functions
Object.assign(logger, logHelpers);

// Create a stream for Morgan HTTP logging
logger.stream = {
  write: (message) => {
    logger.info(message.trim());
  }
};

module.exports = logger;
